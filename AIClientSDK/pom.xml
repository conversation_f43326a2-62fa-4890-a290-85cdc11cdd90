<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.epoint.ebpu</groupId>
	<artifactId>AIClientSDK</artifactId>
	<version>0.0.4-SNAPSHOT</version>
	<packaging>jar</packaging>

	<name>AIClientSDK</name>
	<distributionManagement>
		<repository>
			<id>epoint-nexus</id>
			<url>http://************:8081/nexus/content/repositories/dzjyrelease</url>
		</repository>
		<snapshotRepository>
			<id>epoint-nexus</id>
			<url>http://************:8081/nexus/content/repositories/dzjysnapshot</url>
		</snapshotRepository>
	</distributionManagement>

	<properties>
		<source-version>1.8</source-version>
		<target-version>1.8</target-version>

		<maven-compiler-plugin-version>3.2</maven-compiler-plugin-version>
		<maven-jar-plugin-version>2.4</maven-jar-plugin-version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven.test.skip>true</maven.test.skip>

		<maven-resources-plugin-version>2.6</maven-resources-plugin-version>
		<exec-maven-plugin-version>1.2.1</exec-maven-plugin-version>
		<git.gitpath>.git</git.gitpath>
	</properties>

	<dependencies>
		<!-- 火山方舟AI SDK -->
		<dependency>
			<groupId>com.volcengine</groupId>
			<artifactId>volcengine-java-sdk-ark-runtime</artifactId>
			<version>0.1.153</version>
		</dependency>
		<dependency>
			<groupId>com.epoint</groupId>
			<artifactId>WordCom</artifactId>
			<version>0.0.3</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.5</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.9</version>
		</dependency>
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<version>2.4</version>
			<classifier>jdk15</classifier>
		</dependency>
		<dependency>
			<groupId>com.konghq</groupId>
			<artifactId>unirest-java</artifactId>
			<version>3.13.11</version>
		</dependency>
		<!-- fastjson -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.72</version>
		</dependency>
		<!-- junit单元测试 -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>
		<!-- 通义千问sdk -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>dashscope-sdk-java</artifactId>
			<!-- 请将 'the-latest-version' 替换为最新版本号：https://mvnrepository.com/artifact/com.alibaba/dashscope-sdk-java -->
			<version>2.18.3</version>
		</dependency>

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.9</version> <!-- or another appropriate version -->
		</dependency>
		<!-- Spring Boot Web Starter -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>4.3.29.RELEASE</version>
			<scope>provided</scope>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<!--指定编译版本 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin-version}</version>
				<configuration>
					<source>${source-version}</source>
					<target>${target-version}</target>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>pl.project13.maven</groupId>
				<artifactId>git-commit-id-plugin</artifactId>
				<version>4.0.0</version>
				<executions>
					<execution>
						<id>get-the-git-infos</id>
						<phase>initialize</phase>
						<goals>
							<goal>revision</goal>
						</goals>
					</execution>
					<!-- 绑定validateRevision目标到package阶段 -->
					<execution>
						<id>validate-the-git-infos</id>
						<phase>package</phase>
						<goals>
							<goal>validateRevision</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<!-- 检查的仓库根目录，${project.basedir}：项目根目录，即包含pom.xml文件的目录 -->
					<dotGitDirectory>${project.basedir}/${git.gitpath}</dotGitDirectory>
					<prefix>git</prefix>
					<dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>
					<!-- 是否输出git信息（调试用） -->
					<verbose>true</verbose>
					<!-- 使用本地仓库发布 -->
					<offline>true</offline>
					<gitDescribe>
						<!--提交操作未发现tag时,仅打印提交操作ID -->
						<always>false</always>
					</gitDescribe>
					<validationProperties>
						<validationProperty>
							<!-- 校验失败时提示使用 -->
							<name>validating git dirty</name>
							<!-- 需要校验的属性 dirty:true 表示存在未提交的修改 -->
							<value>${git.dirty}</value>
							<!-- 期望的属性值：false -->
							<shouldMatchTo>false</shouldMatchTo>
						</validationProperty>
					</validationProperties>
					<!-- 配置校验的属性值与期望值不一致是否构建失败 -->
					<validationShouldFailIfNoMatch>false</validationShouldFailIfNoMatch>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>buildnumber-maven-plugin</artifactId>
				<version>1.4</version>
				<configuration>
					<timestampFormat>yyyy-MM-dd HH:mm:ss.S</timestampFormat>
					<timestampPropertyName>buildTime</timestampPropertyName>
				</configuration>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<goal>create-timestamp</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>${maven-jar-plugin-version}</version>
				<configuration>
					<archive>
						<manifest>
							<addDefaultImplementationEntries>true</addDefaultImplementationEntries>
							<addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
						</manifest>
						<manifestEntries>
							<GIT-Revision>${git.commit.id}</GIT-Revision>
							<GIT-Dirty>${git.dirty}</GIT-Dirty>
							<GIT-CommitTime>${git.commit.time}</GIT-CommitTime>
							<GIT-RemoteOriginUrl>${git.remote.origin.url}</GIT-RemoteOriginUrl>
							<GIT-TotalCommitCount>${git.total.commit.count}</GIT-TotalCommitCount>
							<GIT-Branche>${git.branch}</GIT-Branche>
							<Build-Time>${buildTime}</Build-Time>
						</manifestEntries>
					</archive>
					<excludes>
						<exclude>**/allatori.xml</exclude>
						<exclude>**/rebel.xml</exclude>
					</excludes>
				</configuration>
			</plugin>
		</plugins>
		<pluginManagement>
			<plugins>
				<!--This plugin's configuration is used to store Eclipse m2e settings 
					only. It has no influence on the Maven build itself. -->
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>
											org.codehaus.mojo
										</groupId>
										<artifactId>
											buildnumber-maven-plugin
										</artifactId>
										<versionRange>
											[1.4,)
										</versionRange>
										<goals>
											<goal>
												create-timestamp
											</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore></ignore>
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>
</project>
