package com.epoint.ebpu.AIClientSDK;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileFilter;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;

import javax.imageio.ImageIO;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.junit.Test;

import com.alibaba.fastjson.JSON;
import com.epoint.ebpu.AIClientSDK.client.enums.DomainType;
import com.epoint.ebpu.AIClientSDK.dto.BidAIAssessItemFactorDTO;
import com.epoint.ebpu.AIClientSDK.dto.BidAIAssessMethodDTO;
import com.epoint.ebpu.AIClientSDK.dto.BidAIEvaluationResultDTO;
import com.epoint.ebpu.AIClientSDK.dto.BidAIRequirementDTO;
import com.epoint.ebpu.AIClientSDK.dto.BidAITenderFileDTO;
import com.epoint.ebpu.AIClientSDK.service.AIBidClient;
import com.epoint.ebpu.AIClientSDK.service.AIBidClientFactory;
import com.epoint.ebpu.AIClientSDK.service.AIModelType;
import com.epoint.ebpu.AIClientSDK.util.ImageConvertBase64;

import technology.tabula.Utils;

/**
 * Unit test for simple App.
 */
public class AppTest {

	@Test
	public void testassessRuleReasoning() {
		AIBidClient client = AIBidClientFactory.getClient(AIModelType.DOUBAO);
		String result = client.assessRuleReasoning("营业执照", "具备有效的营业执照").getData();
		System.out.println(result);
	}

	public void testpbbfStripper() throws FileNotFoundException {
		AIBidClient client = AIBidClientFactory.getClient(AIModelType.DOUBAO);
		List<BidAIAssessMethodDTO> result = client.pbbfStripper(
				new FileInputStream("C:\\Users\\<USER>\\Desktop\\B3206010318000551001002南通火车东站综合客运枢纽工程施工\\招标文件正文.pdf"))
				.getData();
		System.out.println(JSON.toJSON(result).toString());
	}

	public void testbidFileDataStripper() throws FileNotFoundException {
		AIBidClient client = AIBidClientFactory.getClient(AIModelType.DOUBAO);
		List<BidAIRequirementDTO> requirement = new ArrayList<>();
		BidAIRequirementDTO gq = new BidAIRequirementDTO();
		gq.setRequireName("工期要求");
		requirement.add(gq);
		BidAIRequirementDTO zl = new BidAIRequirementDTO();
		zl.setRequireName("质量要求");
		requirement.add(zl);
		BidAIRequirementDTO kbtime = new BidAIRequirementDTO();
		kbtime.setRequireName("开标时间");
		requirement.add(kbtime);
		BidAIRequirementDTO qyzz = new BidAIRequirementDTO();
		qyzz.setRequireName("企业资质要求");
		requirement.add(qyzz);
		BidAIRequirementDTO zy = new BidAIRequirementDTO();
		zy.setRequireName("项目专业属性");
		requirement.add(zy);
		List<BidAIRequirementDTO> result = client.bidFileDataStripper(
				new FileInputStream("C:\\Users\\<USER>\\Desktop\\B3206010318000551001002南通火车东站综合客运枢纽工程施工\\招标文件正文.pdf"),
				requirement).getData();
		System.out.println(JSON.toJSON(result).toString());
	}

	public void testrequirementContentStripper() throws FileNotFoundException {
		AIBidClient client = AIBidClientFactory.getClient(AIModelType.DOUBAO);

		List<BidAIAssessItemFactorDTO> assessItemFactor = new ArrayList<>();
		BidAIAssessItemFactorDTO e = new BidAIAssessItemFactorDTO();
		e.setItemGuid(UUID.randomUUID().toString());
		e.setItemOrder("1");
		e.setAssessItemName("诚信承诺书");
		e.setItemCriteria("经投标人盖章及法定代表人签字或盖章的承诺书（按照【招标文件】提供“诚信承诺书”格式填写）");
		e.setBidRequirements("“诚信承诺书”格式内容是【】");
		assessItemFactor.add(e);
		BidAIAssessItemFactorDTO e2 = new BidAIAssessItemFactorDTO();
		e2.setItemGuid(UUID.randomUUID().toString());
		e2.setItemOrder("2");
		e2.setAssessItemName("工程质量");
		e2.setItemCriteria("投标函中载明的质量符合第二章“投标人须知”第1.3.3项规定");
		e2.setBidRequirements("质量要求是XXX");
		assessItemFactor.add(e2);
		BidAIAssessItemFactorDTO e3 = new BidAIAssessItemFactorDTO();
		e3.setItemGuid(UUID.randomUUID().toString());
		e3.setItemOrder("3");
		e3.setAssessItemName("其他要求");
		e3.setItemCriteria("无评标办法第 3.3.6 条所列情形");
		e3.setBidRequirements("");
		assessItemFactor.add(e3);
		BidAIAssessItemFactorDTO e4 = new BidAIAssessItemFactorDTO();
		e4.setItemGuid(UUID.randomUUID().toString());
		e4.setItemOrder("4");
		e4.setAssessItemName("其他要求");
		e4.setItemCriteria("符合第二章“投标人须知”第1.4.1项规定的其他要求");
		e4.setBidRequirements("");
		assessItemFactor.add(e4);

		/*List<BidAIAssessMethodDTO> pbbf = client.pbbfStripper(
				new FileInputStream("C:\\Users\\<USER>\\Desktop\\B3206010318000551001002南通火车东站综合客运枢纽工程施工\\招标文件正文.pdf"))
				.getData();
		System.out.println("评标办法：" + JSON.toJSON(pbbf).toString());
		List<BidAIAssessItemFactorDTO> assessItemFactor = new ArrayList<>();
		int xh = 1;
		for (BidAIAssessMethodDTO pfd : pbbf) {
			if ("符合性".equals(pfd.getAssessType())) {// 初步评审
				BidAIAssessItemFactorDTO e = new BidAIAssessItemFactorDTO();
				e.setItemGuid(UUID.randomUUID().toString());
				e.setItemOrder(String.valueOf(xh));
				e.setAssessItemName(pfd.getAssessItemName());
				e.setItemCriteria(pfd.getItemCriteria());
				assessItemFactor.add(e);
				xh++;
			}
		}*/
		List<BidAIAssessItemFactorDTO> result = client.requirementContentStripper(
				new FileInputStream("C:\\Users\\<USER>\\Desktop\\招标文件正文.pdf"),
				assessItemFactor).getData();
		System.out.println(JSON.toJSON(result).toString());
	}
	
	
	public void testresponsestripper() throws FileNotFoundException {
		String tbpdfdir = "C:\\Users\\<USER>\\Desktop\\广州测试1\\测试投标1\\";
		String zbpdf = "C:\\Users\\<USER>\\Desktop\\广州测试1\\招标文件正文.pdf";
		long t = System.currentTimeMillis();

		AIBidClient client = AIBidClientFactory.getClient(AIModelType.DOUBAO);
		// 1、提取评标办法
		List<BidAIAssessMethodDTO> pbbf = client.pbbfStripper(new FileInputStream(zbpdf)).getData();
		System.out.println("评标办法：" + JSON.toJSON(pbbf).toString());
		// 2、评标办法转 评审规则表
		List<BidAIAssessItemFactorDTO> assessItemFactor = new ArrayList<>();
		int xh = 1;
		for (BidAIAssessMethodDTO pfd : pbbf) {
			if ("符合性".equals(pfd.getAssessType())) {// 初步评审
				BidAIAssessItemFactorDTO e = new BidAIAssessItemFactorDTO();
				e.setItemGuid(UUID.randomUUID().toString());
				e.setItemOrder(String.valueOf(xh));
				e.setAssessItemName(pfd.getAssessItemName());
				e.setItemCriteria(pfd.getItemCriteria());
				assessItemFactor.add(e);
				xh++;
			}
		}
		System.out.println("初步评审规则：" + JSON.toJSON(assessItemFactor).toString());
		// 3、评审规则里缺的招标要求再提取
		List<BidAIAssessItemFactorDTO> zbrules = client
				.requirementContentStripper(new FileInputStream(zbpdf), assessItemFactor).getData();
		System.out.println("初步评审规则提取后：" + JSON.toJSON(zbrules).toString());
		// 3、特殊的项目信息一般评标办法里不写，要自己设置提取
		// 4、投标材料准备
		List<BidAITenderFileDTO> tenderFiles = new ArrayList<>();
		// 处理文件
		// pdf转图片base64
		File[] fss = new File(tbpdfdir).listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if (pathname.isDirectory())
					return false;
				else
					return true;
			}
		});
		for (File ff : fss) {
			if (ff.getName().endsWith(".pdf")) {
				String pdfdir = tbpdfdir + ff.getName().replace(".pdf", "");
				File pdfdirfile = new File(pdfdir);
				if (!pdfdirfile.exists()) {
					pdfdirfile.mkdir();
					try (PDDocument document = PDDocument.load(ff)) {
						for (int index = 0; index < document.getNumberOfPages(); index++) {
							BufferedImage image = Utils.pageConvertToImage(document, document.getPage(index), 90,
									ImageType.RGB);
							File img = new File(pdfdir + File.separator + (index + 1) + ".png");
							ImageIO.write(image, "png", img);
							BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
							tbfile.setFileType("png");
							tbfile.setFileContent(ImageConvertBase64.toBase64(img, true));
							tenderFiles.add(tbfile);
						}
					} catch (IOException e1) {
						e1.printStackTrace();
					}
				} else {
					File[] fs = pdfdirfile.listFiles();
					Arrays.sort(fs, new Comparator<File>() {
			            @Override
			            public int compare(File f1, File f2) {
			                // 提取文件名中的数字部分进行比较
			                String name1 = f1.getName();
			                String name2 = f2.getName();
			                // 使用正则表达式匹配文件名中的数字部分
			                String num1 = name1.replaceAll("\\D", "");
			                String num2 = name2.replaceAll("\\D", "");
			                int num1Int = num1.isEmpty() ? 0 : Integer.parseInt(num1);
			                int num2Int = num2.isEmpty() ? 0 : Integer.parseInt(num2);
			                // 先按数字从小到大排序
			                return Integer.compare(num1Int, num2Int);
			            }
			        });
					for (File img : fs) {
						BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
						tbfile.setFileType("png");
						tbfile.setFileContent(ImageConvertBase64.toBase64(img, true));
						tenderFiles.add(tbfile);
					}
				}
			} else if (ff.getName().endsWith(".png") || ff.getName().endsWith(".jpg")
					|| ff.getName().endsWith(".jpeg")) {
				BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
				tbfile.setFileType("png");
				tbfile.setFileContent(ImageConvertBase64.toBase64(ff, true));
				tenderFiles.add(tbfile);
			}
		}
		System.out.println("投标响应材料准备完成");
		// 5、送评审规则表、投标响应材料 提取
		List<BidAIAssessItemFactorDTO> result = client.responseContentStripper(zbrules, tenderFiles).getData();
		System.out.println("提取投标响应结果：" + JSON.toJSON(result).toString());
		System.out.println("耗时：" + (System.currentTimeMillis() - t));
	}

	public void testevaluation() throws FileNotFoundException {
		String tbpdfdir = "C:\\Users\\<USER>\\Desktop\\广州测试1\\测试投标1\\";
		String zbpdf = "C:\\Users\\<USER>\\Desktop\\广州测试1\\招标文件正文.pdf";
		long t = System.currentTimeMillis();

		AIBidClient client = AIBidClientFactory.getClient(AIModelType.DOUBAO);
		// 1、提取评标办法
		List<BidAIAssessMethodDTO> pbbf = client.pbbfStripper(new FileInputStream(zbpdf)).getData();
		System.out.println("评标办法：" + JSON.toJSON(pbbf).toString());
		// 2、评标办法转 评审规则表
		List<BidAIAssessItemFactorDTO> assessItemFactor = new ArrayList<>();
		int xh = 1;
		for (BidAIAssessMethodDTO pfd : pbbf) {
			if ("符合性".equals(pfd.getAssessType())) {// 初步评审
				BidAIAssessItemFactorDTO e = new BidAIAssessItemFactorDTO();
				e.setItemGuid(UUID.randomUUID().toString());
				e.setItemOrder(String.valueOf(xh));
				e.setAssessItemName(pfd.getAssessItemName());
				e.setItemCriteria(pfd.getItemCriteria());
				assessItemFactor.add(e);
				xh++;
			}
		}
		System.out.println("初步评审规则：" + JSON.toJSON(assessItemFactor).toString());
		// 3、评审规则里缺的招标要求再提取
		List<BidAIAssessItemFactorDTO> zbrules = client
				.requirementContentStripper(new FileInputStream(zbpdf), assessItemFactor).getData();
		System.out.println("初步评审规则提取后：" + JSON.toJSON(zbrules).toString());
		// 3、特殊的项目信息一般评标办法里不写，要自己设置提取
		// 4、投标材料准备
		List<BidAITenderFileDTO> tenderFiles = new ArrayList<>();
		// 处理文件
		// pdf转图片base64
		File[] fss = new File(tbpdfdir).listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if (pathname.isDirectory())
					return false;
				else
					return true;
			}
		});
		for (File ff : fss) {
			if (ff.getName().endsWith(".pdf")) {
				String pdfdir = tbpdfdir + ff.getName().replace(".pdf", "");
				File pdfdirfile = new File(pdfdir);
				if (!pdfdirfile.exists()) {
					pdfdirfile.mkdir();
					try (PDDocument document = PDDocument.load(ff)) {
						for (int index = 0; index < document.getNumberOfPages(); index++) {
							BufferedImage image = Utils.pageConvertToImage(document, document.getPage(index), 90,
									ImageType.RGB);
							File img = new File(pdfdir + File.separator + (index + 1) + ".png");
							ImageIO.write(image, "png", img);
							BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
							tbfile.setFileType("png");
							tbfile.setFileContent(ImageConvertBase64.toBase64(img, true));
							tenderFiles.add(tbfile);
						}
					} catch (IOException e1) {
						e1.printStackTrace();
					}
				} else {
					File[] fs = pdfdirfile.listFiles();
					Arrays.sort(fs, new Comparator<File>() {
			            @Override
			            public int compare(File f1, File f2) {
			                // 提取文件名中的数字部分进行比较
			                String name1 = f1.getName();
			                String name2 = f2.getName();
			                // 使用正则表达式匹配文件名中的数字部分
			                String num1 = name1.replaceAll("\\D", "");
			                String num2 = name2.replaceAll("\\D", "");
			                int num1Int = num1.isEmpty() ? 0 : Integer.parseInt(num1);
			                int num2Int = num2.isEmpty() ? 0 : Integer.parseInt(num2);
			                // 先按数字从小到大排序
			                return Integer.compare(num1Int, num2Int);
			            }
			        });
					for (File img : fs) {
						BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
						tbfile.setFileType("png");
						tbfile.setFileContent(ImageConvertBase64.toBase64(img, true));
						tenderFiles.add(tbfile);
					}
				}
			} else if (ff.getName().endsWith(".png") || ff.getName().endsWith(".jpg")
					|| ff.getName().endsWith(".jpeg")) {
				BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
				tbfile.setFileType("png");
				tbfile.setFileContent(ImageConvertBase64.toBase64(ff, true));
				tenderFiles.add(tbfile);
			}
		}
		System.out.println("投标响应材料准备完成");
		// 5、AI智能评标 送评审规则表、招标要求、投标响应材料
		List<BidAIEvaluationResultDTO> result = client.evaluation(zbrules, null, tenderFiles, DomainType.GC).getData();
		System.out.println("评审结果：" + JSON.toJSON(result).toString());
		System.out.println("耗时：" + (System.currentTimeMillis() - t));
	}

	public void testevaluationformark() throws FileNotFoundException {
		String tbpdfdir = "C:\\Users\\<USER>\\Desktop\\广州技术标测试\\测试投标1\\";
		String zbpdf = "C:\\Users\\<USER>\\Desktop\\广州技术标测试\\招标文件正文.pdf";
		long t = System.currentTimeMillis();

		AIBidClient client = AIBidClientFactory.getClient(AIModelType.DOUBAO);
		// 1、提取评标办法
		List<BidAIAssessMethodDTO> pbbf = client.pbbfStripper(new FileInputStream(zbpdf)).getData();
		System.out.println("评标办法：" + JSON.toJSON(pbbf).toString());
		// 2、评标办法转 评审规则表
		List<BidAIAssessItemFactorDTO> assessItemFactor = new ArrayList<>();
		int xh = 1;
		for (BidAIAssessMethodDTO pfd : pbbf) {
			if ("直接打分".equals(pfd.getAssessType()) && pfd.getBlockTag().contains("技术")) {// 技术标评审
				BidAIAssessItemFactorDTO e = new BidAIAssessItemFactorDTO();
				e.setItemGuid(UUID.randomUUID().toString());
				e.setItemOrder(String.valueOf(xh));
				e.setAssessItemName(pfd.getAssessItemName());
				e.setItemCriteria(pfd.getItemCriteria());
				assessItemFactor.add(e);
				xh++;
			}
		}
		// 3、特殊的项目信息一般评标办法里不写，要自己设置提取
		// 4、投标材料准备
		List<BidAITenderFileDTO> tenderFiles = new ArrayList<>();
		// 处理文件
		// pdf转图片base64
		File[] fss = new File(tbpdfdir).listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if (pathname.isDirectory())
					return false;
				else
					return true;
			}
		});
		for (File ff : fss) {
			if (ff.getName().endsWith(".pdf")) {
				String pdfdir = tbpdfdir + ff.getName().replace(".pdf", "");
				File pdfdirfile = new File(pdfdir);
				if (!pdfdirfile.exists()) {
					pdfdirfile.mkdir();
					try (PDDocument document = PDDocument.load(ff)) {
						for (int index = 0; index < document.getNumberOfPages(); index++) {
							BufferedImage image = Utils.pageConvertToImage(document, document.getPage(index), 90,
									ImageType.RGB);
							File img = new File(pdfdir + File.separator + (index + 1) + ".png");
							ImageIO.write(image, "png", img);
							BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
							tbfile.setFileType("png");
							tbfile.setFileContent(ImageConvertBase64.toBase64(img, true));
							tenderFiles.add(tbfile);
						}
					} catch (IOException e1) {
						e1.printStackTrace();
					}
				} else {
					File[] fs = pdfdirfile.listFiles();
					Arrays.sort(fs, new Comparator<File>() {
			            @Override
			            public int compare(File f1, File f2) {
			                // 提取文件名中的数字部分进行比较
			                String name1 = f1.getName();
			                String name2 = f2.getName();
			                // 使用正则表达式匹配文件名中的数字部分
			                String num1 = name1.replaceAll("\\D", "");
			                String num2 = name2.replaceAll("\\D", "");
			                int num1Int = num1.isEmpty() ? 0 : Integer.parseInt(num1);
			                int num2Int = num2.isEmpty() ? 0 : Integer.parseInt(num2);
			                // 先按数字从小到大排序
			                return Integer.compare(num1Int, num2Int);
			            }
			        });
					for (File img : fs) {
						BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
						tbfile.setFileType("png");
						tbfile.setFileContent(ImageConvertBase64.toBase64(img, true));
						tenderFiles.add(tbfile);
					}
				}
			} else if (ff.getName().endsWith(".png") || ff.getName().endsWith(".jpg")
					|| ff.getName().endsWith(".jpeg")) {
				BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
				tbfile.setFileType("png");
				tbfile.setFileContent(ImageConvertBase64.toBase64(ff, true));
				tenderFiles.add(tbfile);
			}
		}
		System.out.println("投标响应材料准备完成");
		// 5、AI智能评标 送评审规则表、招标要求、投标响应材料
		List<BidAIEvaluationResultDTO> result = client.evaluationformark(assessItemFactor, null, tenderFiles, DomainType.GC).getData();
		System.out.println("评审结果：" + JSON.toJSON(result).toString());
		System.out.println("耗时：" + (System.currentTimeMillis() - t));
	}

	public static void main(String[] args) {
		System.out.println(ImageConvertBase64.toBase64(new File("D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\广州样本2\\ai" +
				"评审\\测试\\资格审查文件\\2.png"), true));
	}
}
