package com.epoint.ebpu.AIClientSDK;

import com.alibaba.fastjson.JSON;
import com.epoint.ebpu.AIClientSDK.QwenTestData.QwenTestData_NanTong;
import com.epoint.ebpu.AIClientSDK.client.DeepSeekAiUtils;
import com.epoint.ebpu.AIClientSDK.dto.BidAIAssessItemFactorDTO;
import com.epoint.ebpu.AIClientSDK.dto.BidAIAssessItemFactorPack;
import com.epoint.ebpu.AIClientSDK.dto.BidAIAssessRuleReasonPack;
import com.epoint.ebpu.AIClientSDK.service.*;
import com.epoint.ebpu.AIClientSDK.util.MarkdownUtil;
import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-02
 * @Version
 * @Description
 */
public class TestDeepSeek {
    @Test
    public void testPbbfStripper() throws FileNotFoundException {
//        AIBidClientForDeepSeek.ConstantsConfig.config.setOverallModel(DeepSeekAiUtils.REASONER_MODEL);
        AIBidClient client = AIBidClientFactory.getClient(AIModelType.DEEPSEEK);
        File file = new File("D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\中车样本\\3.物资类-邀请竞标项目\\采购文件.pdf");
//        String zbContent = PDFConvertStringUtil.parseByWordCom(new FileInputStream(file));
//        System.out.println(zbContent);
        client.pbbfStripper(new FileInputStream(file));
    }

    @Test
    public void testAssessRule(){
        AIBidClientForDeepSeek.ConstantsConfig.config.setOverallModel(DeepSeekAiUtils.REASONER_MODEL);
        AIBidClient client = AIBidClientFactory.getClient(AIModelType.DEEPSEEK);
        BidAIAssessRuleReasonPack pack =client.assessRuleReasoning("监理业绩", "投标人2019年1月1" +
                "日至今，每完成过一个质量合格的类似项目工程监理业绩得2分，本项最多得10分");
        System.out.println(JSON.toJSON(pack).toString());
    }

    @Test
    public void testRequirementContentStripper() throws FileNotFoundException {
        AIBidClientForDeepSeek.ConstantsConfig.config.setOverallModel(DeepSeekAiUtils.REASONER_MODEL);
        AIBidClient client = AIBidClientFactory.getClient(AIModelType.DEEPSEEK);
//        File file = new File("D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\广州样本2\\房屋建筑\\黄阁智造创新工业园项目第三方监测和检测\\黄阁智造创新工业园项目第三方监测和检测.pdf");
        File file = new File("D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\南通样本\\B3206010318000551001002南通火车东站综合客运枢纽工程施工\\B3206010318000551001002南通火车东站综合客运枢纽工程施工\\招标文件正文.pdf");

        List<Map<String, String>> pbbf = MarkdownUtil.md2List(new QwenTestData_NanTong().zbyqToStrip());
        List<BidAIAssessItemFactorDTO> assessItemFactor = new ArrayList<>();
        for (Map<String, String> map : pbbf) {
            BidAIAssessItemFactorDTO e = new BidAIAssessItemFactorDTO();
            e.setItemOrder(map.get("序号"));
            e.setItemGuid(UUID.randomUUID().toString());
            e.setAssessItemName(map.get("审查点"));
            e.setItemCriteria(map.get("评审要求"));
            e.setBidRequirements(map.get("招标文件规定"));
            e.setTenderResponse(map.get("投标响应情况"));
            e.setAssessRules(map.get("AI评审规则"));
            assessItemFactor.add(e);

        }

        BidAIAssessItemFactorPack result = client.requirementContentStripper(
                new FileInputStream(file),
                assessItemFactor);
        System.out.println(JSON.toJSON(result).toString());
    }
}
