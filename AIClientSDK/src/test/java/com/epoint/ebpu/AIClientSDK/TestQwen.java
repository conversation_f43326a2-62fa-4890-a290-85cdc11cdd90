package com.epoint.ebpu.AIClientSDK;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.epoint.ebpu.AIClientSDK.QwenTestData.QwenTestData_BaoHua;
import com.epoint.ebpu.AIClientSDK.QwenTestData.QwenTestData_GuangZhou;
import com.epoint.ebpu.AIClientSDK.QwenTestData.QwenTestData_NanTong;
import com.epoint.ebpu.AIClientSDK.QwenTestData.QwenTestData_ZhongChe;
import com.epoint.ebpu.AIClientSDK.client.QwenAiUtils;
import com.epoint.ebpu.AIClientSDK.client.enums.DomainType;
import com.epoint.ebpu.AIClientSDK.client.prompts.QwenPrompts;
import com.epoint.ebpu.AIClientSDK.client.vo.ResultVO;
import com.epoint.ebpu.AIClientSDK.dto.*;
import com.epoint.ebpu.AIClientSDK.service.AIBidClient;
import com.epoint.ebpu.AIClientSDK.service.AIBidClientFactory;
import com.epoint.ebpu.AIClientSDK.service.AIBidClientForQwen;
import com.epoint.ebpu.AIClientSDK.service.AIModelType;
import com.epoint.ebpu.AIClientSDK.util.ImageConvertBase64;
import com.epoint.ebpu.AIClientSDK.util.MarkdownUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.junit.Test;
import org.slf4j.LoggerFactory;
import technology.tabula.Utils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-03-14
 * @Version
 * @Description
 */
public class TestQwen {
    protected static final org.slf4j.Logger logger = LoggerFactory.getLogger(TestQwen.class);

    @Test
    public void testAssessRule(){
        BidAIAssessRuleReasonPack pack = new AIBidClientForQwen().assessRuleReasoning("监理业绩", "投标人2019年1月1" +
                "日至今，每完成过一个质量合格的类似项目工程监理业绩得2分，本项最多得10分");
        System.out.println(JSON.toJSON(pack).toString());
    }

    @Test
    public void testPbbfStripper() throws FileNotFoundException {
//        File file = new File("D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\中车样本\\3.物资类-邀请竞标项目\\采购文件.pdf");
//        File file = new File("D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\广州样本2\\ai评审\\招文-广东科学中心科普讲解演播厅装修工程项目施工专业承包-上传版.pdf");
        File file = new File("D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\南通样本\\B3206010318000551001002南通火车东站综合客运枢纽工程施工\\B3206010318000551001002南通火车东站综合客运枢纽工程施工\\招标文件正文.pdf");
//        String zbContent = PDFConvertStringUtil.parseByWordCom(new FileInputStream(file));
//        System.out.println(zbContent);
        BidAIAssessMethodPack bidAIAssessMethodPack = new AIBidClientForQwen().pbbfStripper(new FileInputStream(file));
        System.out.println(JSON.toJSON(bidAIAssessMethodPack).toString());
    }

    @Test
    public void testRequirementContentStripper() throws FileNotFoundException {
//        File file = new File("D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\广州样本2\\房屋建筑\\黄阁智造创新工业园项目第三方监测和检测\\黄阁智造创新工业园项目第三方监测和检测.pdf");
        File file = new File("D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\宝华样本\\招投标文件\\起重机\\技术参数文件\\武钢热轧厂1580单元板坯库吊车更新项目起重机设备招标技术文件.pdf");

//        List<Map<String, String>> pbbf = MarkdownUtil.md2List(new QwenTestData_NanTong().pbbfJson);
//        // 解析后按标准格式返回
//        List<BidAIAssessMethodDTO> lst = new ArrayList<>();
//        for (Map<String, String> r : pbbf) {
//            BidAIAssessMethodDTO p = new BidAIAssessMethodDTO();
//            p.setAssessItemName(r.get("评审因素"));
//            p.setItemCriteria(r.get("评审标准"));
//            p.setBlockTag(r.get("评审项"));
//            p.setAssessType(r.get("评审方式"));
//            p.setMark(r.get("分值"));
//            lst.add(p);
//        }
//
//        List<BidAIAssessItemFactorDTO> assessItemFactor = new ArrayList<>();
//        int xh = 1;
//        for (BidAIAssessMethodDTO pfd : lst) {
//            if("符合性".equals(pfd.getAssessType())){// 初步评审
//                BidAIAssessItemFactorDTO e = new BidAIAssessItemFactorDTO();
//                e.setItemGuid(UUID.randomUUID().toString());
//                e.setItemOrder(String.valueOf(xh));
//                e.setAssessItemName(pfd.getAssessItemName());
//                e.setItemCriteria(pfd.getItemCriteria());
//                e.setBidRequirements("");
//                assessItemFactor.add(e);
//                xh++;
//            }
//        }
        List<Map<String, String>> pbbf = MarkdownUtil.md2List(new QwenTestData_BaoHua().zbyqToStrip());
        List<BidAIAssessItemFactorDTO> assessItemFactor = new ArrayList<>();
        for (Map<String, String> map : pbbf) {
            BidAIAssessItemFactorDTO e = new BidAIAssessItemFactorDTO();
            e.setItemOrder(map.get("序号"));
            e.setItemGuid(map.getOrDefault("唯一标识",UUID.randomUUID().toString()) );
            e.setAssessItemName(map.get("审查点"));
            e.setItemCriteria(map.get("评审要求"));
            e.setBidRequirements(map.get("招标文件规定"));
            e.setTenderResponse(map.get("投标响应情况"));
            e.setAssessRules(map.get("AI评审规则"));
            assessItemFactor.add(e);

        }


        BidAIAssessItemFactorPack result = new AIBidClientForQwen().requirementContentStripper(
                new FileInputStream(file),
                assessItemFactor);
        System.out.println(JSON.toJSON(result).toString());
    }

    @Test
    public void testbidFileDataStripper() throws FileNotFoundException {
        AIBidClient client = AIBidClientFactory.getClient(AIModelType.QWEN);
        List<BidAIRequirementDTO> requirement = new ArrayList<>();
        BidAIRequirementDTO gq = new BidAIRequirementDTO();
        gq.setRequireName("工期要求");
        requirement.add(gq);
        BidAIRequirementDTO zl = new BidAIRequirementDTO();
        zl.setRequireName("质量要求");
        requirement.add(zl);
        BidAIRequirementDTO kbtime = new BidAIRequirementDTO();
        kbtime.setRequireName("开标时间");
        requirement.add(kbtime);
        BidAIRequirementDTO qyzz = new BidAIRequirementDTO();
        qyzz.setRequireName("企业资质要求");
        requirement.add(qyzz);
        BidAIRequirementDTO zy = new BidAIRequirementDTO();
        zy.setRequireName("项目专业属性");
        requirement.add(zy);
        BidAIRequirementPack result = client.bidFileDataStripper(
                new FileInputStream("D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\广州样本2\\房屋建筑\\电白沉香大健康产业园建设项目（观珠镇沙垌村沉香墟基础设施建设工程）勘察及初步设计\\电白沉香大健康产业园建设项目（观珠镇沙垌村沉香墟基础设施建设工程）勘察及初步设计.pdf"),
                requirement);
        System.out.println(JSON.toJSON(result).toString());
    }

    @Test
    public void testevaluation() throws FileNotFoundException {
//        String tbpdfdir ="D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\广州样本2\\ai评审\\测试\\";
//        String tbpdfdir = "D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\中车样本\\3.物资类-邀请竞标项目\\投标文件\\欧特美交通科技股份有限公司\\资审商务";
        String zbpdf = "D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\模型比对\\资质证书有效期不通过的\\A3206010318001025001001\\招标文件正文.pdf";
        String tbpdfdir = "D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\模型比对\\资质证书有效期不通过的\\A3206010318001025001001\\江苏元戎建设有限公司";
        long t = System.currentTimeMillis();
        AIBidClient client = AIBidClientFactory.getClient(AIModelType.QWEN);

//        List<BidAIAssessItemFactorDTO> zbrules=getRequirementFromFile(zbpdf);
        List<BidAIAssessItemFactorDTO> zbrules=getRequirementFromJson();
        System.out.println("初步评审规则提取后：" + JSON.toJSON(zbrules).toString());
        // 3、特殊的项目信息一般评标办法里不写，要自己设置提取
        // 4、投标材料准备
        List<BidAITenderFileDTO> tenderFiles = new ArrayList<>();
        // 处理文件
        // pdf转图片base64
        File[] fss = new File(tbpdfdir).listFiles(new FileFilter() {
            @Override
            public boolean accept(File pathname) {
                if (pathname.isDirectory())
                    return false;
                else
                    return true;
            }
        });
        for (File ff : fss) {
            if (ff.getName().endsWith(".pdf")) {
                String pdfdir = tbpdfdir+"\\" + ff.getName().replace(".pdf", "");
                File pdfdirfile = new File(pdfdir);
                if (!pdfdirfile.exists()) {
                    pdfdirfile.mkdir();
                    try (PDDocument document = PDDocument.load(ff)) {
                        for (int index = 0; index < document.getNumberOfPages(); index++) {
                            BufferedImage image = Utils.pageConvertToImage(document, document.getPage(index), 90,
                                    ImageType.RGB);
                            File img = new File(pdfdir + File.separator + (index + 1) + ".png");
                            ImageIO.write(image, "png", img);
                            BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
                            tbfile.setFileName(img.getName());
                            tbfile.setFileType("png");
                            tbfile.setFilePath(img.toString());
                            tbfile.setFileContent(ImageConvertBase64.toBase64(img, true));
                            tenderFiles.add(tbfile);
                        }
                    } catch (IOException e1) {
                        e1.printStackTrace();
                    }
                } else {
                    for (File img : pdfdirfile.listFiles()) {
                        BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
                        tbfile.setFileName(img.getName());
                        tbfile.setFileType("png");
                        tbfile.setFilePath(img.toString());
                        tbfile.setFileContent(ImageConvertBase64.toBase64(img, true));
                        tenderFiles.add(tbfile);
                    }
                }
            } else if (ff.getName().endsWith(".png") || ff.getName().endsWith(".jpg")
                    || ff.getName().endsWith(".jpeg")) {
                BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
                tbfile.setFileName(ff.getName());
                tbfile.setFileType("png");
                tbfile.setFilePath(ff.toString());
                tbfile.setFileContent(ImageConvertBase64.toBase64(ff, true));
                tenderFiles.add(tbfile);
            }
        }
//        System.out.println("投标响应材料：" + JSON.toJSON(tenderFiles).toString());
        tenderFiles.sort((a,b)->{
            String nameA = a.getFileName().split("\\.")[0];
            String nameB = b.getFileName().split("\\.")[0];
            //先按照长度，后按照字符
            int lengthComp=nameA.length()-nameB.length();
            if(lengthComp!=0){
                return lengthComp;
            }
            return nameA.compareTo(nameB);
        });
        // 5、AI智能评标 送评审规则表、招标要求、投标响应材料
//        BidAIEvaluationResultPack result = client.evaluation(zbrules.stream().skip(10).limit(5).collect(Collectors.toList()), null,
//                tenderFiles.stream().limit(10).collect(Collectors.toList()), null);
        BidAIEvaluationResultPack result =
                client.evaluation(zbrules.stream().skip(0).limit(24).collect(Collectors.toList()), null,tenderFiles,
                        null);

        List<Map<String, String>> saveList= new ArrayList<>();
        for (int i = 0; i < result.getData().size(); i++) {
            Map<String, String> map=new LinkedHashMap<>();
            map.put("序号",String.valueOf(i+1));
            map.put("审查点",result.getData().get(i).getItemGuid());
            map.put("评审要求",result.getData().get(i).getItemCriteria());
            map.put("招标文件规定",result.getData().get(i).getBidRequirements());
            map.put("投标响应情况",result.getData().get(i).getTenderResponse());
//            map.put("AI评审规则",datum.get);
            map.put("AI评审理由",result.getData().get(i).getReason());
            map.put("AI评审结论",result.getData().get(i).getResult());
            saveList.add(map);
        }
        // 保存评审结果md
        try (FileWriter w = new FileWriter(tbpdfdir + "评审结果_Qwen.md")) {
            w.write( MarkdownUtil.list2MD(saveList));
            w.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }

        System.out.println("评审结果：" + JSON.toJSON(result).toString());
        System.out.println("耗时：" + (System.currentTimeMillis()-t));
    }

    private List<BidAIAssessItemFactorDTO> getRequirementFromJson(){
        List<Map<String, String>> requiremd = MarkdownUtil.md2List(new QwenTestData_BaoHua().requirementContent);
        // 解析后按标准格式返回
        List<BidAIAssessItemFactorDTO> lst = new ArrayList<>();
        for (Map<String, String> r : requiremd) {
            BidAIAssessItemFactorDTO p = new BidAIAssessItemFactorDTO();
            p.setItemGuid(r.get("唯一标识"));
            p.setItemOrder(r.get("序号"));
            p.setAssessItemName(r.get("审查点"));
            p.setItemCriteria(r.get("评审要求"));
            p.setBidRequirements(r.get("招标文件规定"));
            p.setTenderResponse(r.get("投标响应情况"));
            lst.add(p);
        }
        return lst;
    }

    private List<BidAIAssessItemFactorDTO> getRequirementFromFile(String zbpdf) throws FileNotFoundException {
        //有豆包的，先去豆包的
        String mdPath= new File(zbpdf).getParent()+"\\评审规则表.md";
        File mdFile = new File(mdPath);
        if (mdFile.exists()) {
            FileReader reader = new FileReader(mdFile);
            try (BufferedReader bufferedReader = new BufferedReader(reader)){
                StringBuilder sb=new StringBuilder();
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    sb.append(line+"\n");  // 输出每一行
                }
                List<Map<String, String>> maps = MarkdownUtil.md2List(sb.toString());
                List<BidAIAssessItemFactorDTO> rets = new ArrayList<>();
                for (Map<String, String> map : maps) {
                    BidAIAssessItemFactorDTO factorDTO = new BidAIAssessItemFactorDTO();
                    factorDTO.setItemOrder(map.get("序号"));
                    factorDTO.setAssessItemName(map.get("审查点"));
                    factorDTO.setItemCriteria(map.get("评审要求"));
                    factorDTO.setItemGuid(UUID.randomUUID().toString());
                    factorDTO.setBidRequirements(map.get("招标文件规定"));
                    rets.add(factorDTO);

                }
                return rets;
            }
            catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        String saveInfoPath = new File(zbpdf).getParent()+"\\zbrule.txt";
        File file = new File(saveInfoPath);
        if (file.exists()) {
            FileReader reader = new FileReader(file);
            try (BufferedReader bufferedReader = new BufferedReader(reader)){
                StringBuilder sb=new StringBuilder();
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    sb.append(line);  // 输出每一行
                }
                List<BidAIAssessItemFactorDTO> rets = JSONArray.parseArray(sb.toString(), BidAIAssessItemFactorDTO.class);
                return rets;
            }
            catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        AIBidClient client = AIBidClientFactory.getClient(AIModelType.QWEN);
        // 1、提取评标办法
        BidAIAssessMethodPack pbbf = client.pbbfStripper(new FileInputStream(zbpdf));
        System.out.println("评标办法：" + JSON.toJSON(pbbf).toString());
        // 2、评标办法转 评审规则表
        List<BidAIAssessItemFactorDTO> assessItemFactor = new ArrayList<>();
        int xh = 1;
        for (BidAIAssessMethodDTO pfd : pbbf.getData()) {
            if("符合性".equals(pfd.getAssessType())){// 初步评审
                BidAIAssessItemFactorDTO e = new BidAIAssessItemFactorDTO();
                e.setItemGuid(UUID.randomUUID().toString());
                e.setItemOrder(String.valueOf(xh));
                e.setAssessItemName(pfd.getAssessItemName());
                e.setItemCriteria(pfd.getItemCriteria());
                assessItemFactor.add(e);
                xh++;
            }
        }
        System.out.println("初步评审规则：" + JSON.toJSON(assessItemFactor).toString());
        // 3、评审规则里缺的招标要求再提取
        List<BidAIAssessItemFactorDTO> zbrules = client.requirementContentStripper(new FileInputStream(zbpdf),
                assessItemFactor).getData();
        // 保存结果
        try (FileWriter w = new FileWriter(saveInfoPath)) {
            w.write(JSON.toJSON(zbrules).toString());
            w.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return zbrules;
    }

    @Test
    public void testTechEvaluation(){
        List<Map<String, String>> maps = MarkdownUtil.md2List(new QwenTestData_BaoHua().techStr);
        List<Map<String, String>> maps_BasePFD = MarkdownUtil.md2List(new QwenTestData_BaoHua().zbyqToStrip());
        List<BidAIAssessItemFactorDTO> list = maps.stream()
//                .filter(p -> "技术评审".equals(p.get("评审项")))
                .map(p->{
                    Optional<Map<String, String>> pfdinfo = maps_BasePFD.stream().filter(k -> k.get("唯一标识").equals(p.get("唯一标识"))).findFirst();
                    BidAIAssessItemFactorDTO itemFactorDTO = new BidAIAssessItemFactorDTO();
                    itemFactorDTO.setAssessItemName(p.get("评审因素"));
                    itemFactorDTO.setItemOrder(p.get("序号"));
                    itemFactorDTO.setAssessType(p.get("评审方式"));
                    itemFactorDTO.setItemGuid(p.get("唯一标识"));
                    itemFactorDTO.setItemCriteria(p.get("评审标准"));
                    itemFactorDTO.setAssessRules(pfdinfo.isPresent()?pfdinfo.get().get("AI评审规则"):"");
                    itemFactorDTO.setBidRequirements(p.get("招标文件规定"));
                    itemFactorDTO.setTenderResponse(p.get("投标响应情况"));
                    itemFactorDTO.setMark(pfdinfo.isPresent()?pfdinfo.get().get("分值"):"10");

                    return itemFactorDTO;
                }).collect(Collectors.toList());

        String tbpdfdir = "D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\宝华样本\\招投标文件\\起重机\\技术参数文件\\技术投标文件";
        long t = System.currentTimeMillis();

        // 3、特殊的项目信息一般评标办法里不写，要自己设置提取
        // 4、投标材料准备
        List<BidAITenderFileDTO> tenderFiles = new ArrayList<>();
        // 处理文件
        // pdf转图片base64
        File[] fss = new File(tbpdfdir).listFiles(new FileFilter() {
            @Override
            public boolean accept(File pathname) {
                if (pathname.isDirectory())
                    return false;
                else
                    return true;
            }
        });
        for (File ff : fss) {
            if (ff.getName().endsWith(".pdf")) {
                String pdfdir = tbpdfdir + ff.getName().replace(".pdf", "");
                File pdfdirfile = new File(pdfdir);
                if (!pdfdirfile.exists()) {
                    pdfdirfile.mkdir();
                    try (PDDocument document = PDDocument.load(ff)) {
                        for (int index = 0; index < document.getNumberOfPages(); index++) {
                            BufferedImage image = Utils.pageConvertToImage(document, document.getPage(index), 90,
                                    ImageType.RGB);
                            File img = new File(pdfdir + File.separator + (index + 1) + ".png");
                            ImageIO.write(image, "png", img);
                            BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
                            tbfile.setFileName(img.getName());
                            tbfile.setFileType("png");
                            tbfile.setFilePath(img.toString());
                            tbfile.setFileContent(ImageConvertBase64.toBase64(img, true));
                            tenderFiles.add(tbfile);
                        }
                    } catch (IOException e1) {
                        e1.printStackTrace();
                    }
                } else {
                    for (File img : pdfdirfile.listFiles()) {
                        BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
                        tbfile.setFileName(img.getName());
                        tbfile.setFileType("png");
                        tbfile.setFilePath(img.toString());
                        tbfile.setFileContent(ImageConvertBase64.toBase64(img, true));
                        tenderFiles.add(tbfile);
                    }
                }
            } else if (ff.getName().endsWith(".png") || ff.getName().endsWith(".jpg")
                    || ff.getName().endsWith(".jpeg")) {
                BidAITenderFileDTO tbfile = new BidAITenderFileDTO();
                tbfile.setFileName(ff.getName());
                tbfile.setFileType("png");
                tbfile.setFilePath(ff.toString());
                tbfile.setFileContent(ImageConvertBase64.toBase64(ff, true));
                tenderFiles.add(tbfile);
            }
        }
//        System.out.println("投标响应材料：" + JSON.toJSON(tenderFiles).toString());
        tenderFiles.sort((a,b)->{
            String nameA = a.getFileName().split("\\.")[0];
            String nameB = b.getFileName().split("\\.")[0];
            //先按照长度，后按照字符
            int lengthComp=nameA.length()-nameB.length();
            if(lengthComp!=0){
                return lengthComp;
            }
            return nameA.compareTo(nameB);
        });
        AIBidClient client = AIBidClientFactory.getClient(AIModelType.QWEN);

        System.out.println("需提取投标响应条数：" + list.size());
        BidAIEvaluationResultPack evaluationformark =
                client.evaluationformark(list.stream().skip(4).limit(4).collect(Collectors.toList()),
                new ArrayList<>(),
                tenderFiles.stream().skip(0).limit(40).collect(Collectors.toList()),
                DomainType.GC);

        System.out.println("评审结果：" + JSON.toJSON(evaluationformark).toString());
        System.out.println("耗时：" + (System.currentTimeMillis()-t));
    }

    @Test
    public void testMarking(){
        List<Map<String, String>> mapsStriped=new ArrayList<>();
        for (int i = 0; i < new QwenTestData_BaoHua().techResponseArrays.size(); i++) {
            if(i==0){
                mapsStriped = MarkdownUtil.md2List(new QwenTestData_BaoHua().techResponseArrays.get(i));
                for (Map<String, String> mapStriped : mapsStriped) {
                    StringBuilder stringBuilder=new StringBuilder();
                    StringBuilder sB_Cut=new StringBuilder();
                    String xyqk_Now = mapStriped.getOrDefault("投标响应情况", "");
                    mapStriped.put("投标响应情况_全文",stringBuilder.append("第").append(i+1).append("轮提取情况--").append(xyqk_Now).toString());
                    //定位页码、投标响应情况_截取处理
                    if(StringUtils.isNotBlank(mapStriped.getOrDefault("定位页码", ""))
                            &&!"无".equals(mapStriped.getOrDefault("定位页码", ""))){
                        mapStriped.put("定位页码",mapStriped.get("定位页码"));
                        //截取响应情况，用户不需要看到每一轮的优点，缺点
                        mapStriped.put("投标响应情况",
                                sB_Cut.append("第").append(i+1).append("轮提取情况--").append(getMiddleStr(xyqk_Now,
                                        "响应度说明","优点")).toString());
                    }
                    else {
                        mapStriped.put("定位页码","");
                        mapStriped.put("投标响应情况","");
                    }
                }
            }
            else {
                //将提取完的信息加到mapsStriped
                List<Map<String, String>> mapsInvoke = MarkdownUtil.md2List((new QwenTestData_BaoHua().techResponseArrays.get(i)));
                for (Map<String, String> mapStriped : mapsStriped) {
                    Optional<Map<String, String>> first = mapsInvoke.stream().filter(p -> p.get("唯一标识").equals(mapStriped.get("唯一标识"))).findFirst();
                    if(first.isPresent()){
                        String xyqk_Now=first.get().get("投标响应情况");
                        StringBuilder stringBuilder = new StringBuilder(mapStriped.get("投标响应情况_全文"));
                        StringBuilder sB_Cut=new StringBuilder(mapStriped.getOrDefault("投标响应情况",""));
                        if(!StringUtils.endsWith(mapStriped.get("投标响应情况_全文"),"。")){
                            stringBuilder.append("。");
                            sB_Cut.append("。");//情况和投标响应情况一样的，就不拆分情况了
                        }
                        stringBuilder.append("第").append(i+1).append("轮提取情况--").append(xyqk_Now);

                        mapStriped.put("投标响应情况_全文", stringBuilder.toString());
                        //定位页面处理
                        String dwym_Now = findAndAddNum(first.get().getOrDefault("定位页码", ""), i * QwenAiUtils.IMAGELIMITNUM_MODEL, i * QwenAiUtils.IMAGELIMITNUM_MODEL);
                        // 页码为无，说明未提取到，直接跳过
                        if(!"无".equals(dwym_Now)){
                            mapStriped.put("定位页码",mapStriped.get("定位页码")+","+dwym_Now);
                            mapStriped.put("投标响应情况",sB_Cut.append("第").append(i+1).append("轮提取情况--").append(getMiddleStr(xyqk_Now,"响应度说明","优点")).toString());
                        }
                    }
                }
            }
        }

        List<Map<String, String>> mapsAssessItem = MarkdownUtil.md2List(new QwenTestData_BaoHua().techStr);
        List<Map<String, String>> zbyqMaps = MarkdownUtil.md2List(new QwenTestData_BaoHua().zbyqToStrip());

        StringBuilder psContent_Assess = new StringBuilder();
        mapsStriped.forEach(p->{
            Optional<Map<String, String>> assessItem = mapsAssessItem.stream().filter(k -> k.get("唯一标识").equals(p.get(
                    "唯一标识"))).findFirst();
            Optional<Map<String, String>> zbyq = zbyqMaps.stream().filter(k -> k.get("唯一标识").equals(p.get(
                    "唯一标识"))).findFirst();
            p.remove("定位页码");
            p.remove("投标响应情况_全文");
            p.put("招标文件规定",assessItem.get().get("招标文件规定"));
            p.put("AI评审规则",zbyq.get().get("AI评审规则"));
            p.put("分数上限","10");//写死10分
            p.put("得分","");
            p.put("得分理由","");
        });
        psContent_Assess.append("响应情况打分表内容:").append(MarkdownUtil.list2MD(mapsStriped)).append("\n ");

        String modelValue= QwenAiUtils.MODEL_QWEN_25_32B;
        ResultVO resultVO = QwenAiUtils.callAndLogtime(logger, "AI评分评分",
                r -> QwenAiUtils.baseChatWithModel(QwenPrompts.getTechAssessResultPrompt(), r, modelValue, "", ""),
                psContent_Assess.toString());

        System.out.println(resultVO);
    }

    @Test
    public void testTenderHorizontalEval(){
        BidAIAssessItemFactorDTO assessItemFactor=new BidAIAssessItemFactorDTO();
        assessItemFactor.setAssessItemName("企业业绩有效性评审");
        assessItemFactor.setAssessRules("单位横向对比，业绩条数最多的的满分，其他依次递减，步长为1，第二名的扣1分，第三名的扣2分，排名相同的可得相同分数");
        assessItemFactor.setBidRequirements("提取出的业绩均可算做为有效业绩");
        assessItemFactor.setItemGuid("423deb3c-63ec-886e-39f2-0ac6708bee3b");
        assessItemFactor.setItemCriteria("投标人自XXXX年 XX月XX日（以“交（竣）工验收证明”上的验收日期为准），承担过单项合同金额不低于XX万元XXXX的类似工程");
        assessItemFactor.setMark("10");

        //塞入三家单位
        List<BidAIHorizontalEvalDTO> preAIEvaluations=new ArrayList<>();
        BidAIHorizontalEvalDTO evalDTO=new BidAIHorizontalEvalDTO();
        evalDTO.setTenderGuid("7d4f0dcc-f967-887f-2bda-fcae6b00ea40");
        evalDTO.setTenderResponse("投标文件中“类似项目情况表”中业绩存在3条，分别是：<br>1. 中国医药城商务中心项目（包含鲁班奖），开工日期2014.10.20，竣工日期2018.5.12，合同价格874200000元，类似工程描述为建筑面积：81343㎡，造价：87420万元；<br>2. AC19013地块建设工程设计、采购、施工总承包项目（含中标公示截图），开工日期2019.11.30，竣工日期2021.6.20，合同价格524160000元，类似工程描述为建筑面积：96115㎡，造价：52416万元；<br>3. 南通国际会展中心酒店项目施工总承包（含中标公示截图），开工日期2020.10.18，竣工日期2022.9.21，合同价格618309939.32元，类似工程描述为建筑面积：64900㎡，造价：61830.99万元");
        preAIEvaluations.add(evalDTO);

        evalDTO=new BidAIHorizontalEvalDTO();
        evalDTO.setTenderGuid("0a7c03c0-811b-36d5-1d5c-c205d4e88693");
        evalDTO.setTenderResponse("投标文件中“类似项目情况表”中业绩存在【2】条，分别是【三山科创中心（9 - 11座）土建安装工程（项目所在地广东佛山，发包人佛山市南海金智投资有限公司，合同价格345449432元，开工日期2019.08.08，竣工日期2022.08.08，承担合同范围内全部内容，工程质量合格）；山西省疾病预防控制中心新建项目设计施工总承包（项目所在地山西太原，发包人山西省疾病预防控制中心，合同价格395904100元，开工日期2021.08.25，竣工日期2023.08.30，承担合同范围内全部内容，工程质量合格）】");
        preAIEvaluations.add(evalDTO);


        evalDTO=new BidAIHorizontalEvalDTO();
        evalDTO.setTenderGuid("37fee6c1-7c97-3cb3-8d66-375384d9ed68");
        evalDTO.setTenderResponse("投标文件中“类似项目情况表”中业绩存在【2】条，分别是【三山科创中心（9 - 11座）土建安装工程（项目所在地广东佛山，发包人佛山市南海金智投资有限公司，合同价格345449432元，开工日期2019.08.08，竣工日期2022.08.08，承担合同范围内全部内容，工程质量合格）；山西省疾病预防控制中心新建项目设计施工总承包（项目所在地山西太原，发包人山西省疾病预防控制中心，合同价格395904100元，开工日期2021.08.25，竣工日期2023.08.30，承担合同范围内全部内容，工程质量合格）】");
        preAIEvaluations.add(evalDTO);

        AIBidClient client = AIBidClientFactory.getClient(AIModelType.QWEN);
        BidAIHorizontalEvalPack bidAIHorizontalEvalPack = client.tenderHorizontalEval(assessItemFactor, null, DomainType.GC, preAIEvaluations);

        System.out.println("评审结果：" + JSON.toJSON(bidAIHorizontalEvalPack).toString());
    }

    /**
     * 找到字符串中的数字，并加上一个数字
     *
     * @param str
     * @param addNumber
     * @param originNumber 初始基数编码
     * @return
     */
    protected String findAndAddNum(String str,int addNumber,int originNumber){
        // 正则表达式：匹配整数
        String regex = "\\d+";

        // 创建 Pattern 对象
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);

        // 用来保存修改后的字符串
        StringBuffer result = new StringBuffer();

        // 查找所有数字并进行替换
        while (matcher.find()) {
            // 提取数字并转换为整数
            int number = Integer.parseInt(matcher.group());

            // 将数字增加指定的值
            number += addNumber;
            if(number<originNumber){
                number += originNumber;
            }

            // 将新的数字替换回原字符串
            matcher.appendReplacement(result, String.valueOf(number));
        }
        // 将剩余部分添加到结果中
        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 获取两个字符串中间的内容，并兼容markdown格式
     *
     * @param originS
     * @param preS
     * @param afterS
     * @return
     */
    protected String getMiddleStr(String originS,String preS,String afterS){
        String regex = preS+"\\*?\\*?[：:]?(.+?)\\*?\\*?"+afterS;

        // 创建 Pattern 对象
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(originS);

        // 查找并输出匹配的部分
        if (matcher.find()) {
            // 获取第一个捕获组的内容（即"响应度说明"与"优点"之间的部分）
            return matcher.group(1);
        } else {
            return originS;
        }
    }
}
