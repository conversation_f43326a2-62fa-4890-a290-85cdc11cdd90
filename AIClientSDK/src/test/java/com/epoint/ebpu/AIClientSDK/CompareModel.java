package com.epoint.ebpu.AIClientSDK;

import java.awt.image.BufferedImage;
import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.imageio.ImageIO;

import com.epoint.ebpu.AIClientSDK.client.QwenAiUtils;
import com.epoint.ebpu.AIClientSDK.client.VolcengineAiUtils;
import com.epoint.ebpu.AIClientSDK.util.ImageConvertBase64;
import com.epoint.ebpu.AIClientSDK.util.PDFConvertStringUtil;
import com.itextpdf.styledxmlparser.jsoup.internal.StringUtil;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;

import org.junit.Test;
import org.slf4j.LoggerFactory;
import technology.tabula.Utils;

public class CompareModel {
	protected static final org.slf4j.Logger logger = LoggerFactory.getLogger(CompareModel.class);

	@Test
	public  void testDouBao() {
		long t = System.currentTimeMillis();
		String zbpdf = "D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\模型比对\\2B3206010318000551001002南通火车东站综合客运枢纽工程施工\\招标文件正文.pdf";
		String tbpdfdir = "D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\模型比对\\2B3206010318000551001002南通火车东站综合客运枢纽工程施工\\投标文件\\安徽省中瀚建设工程有限公司";

		// 初始化大模型sdk
		VolcengineAiUtils.init("");

		// 评审规则表组装，提取招标要求
		String rulemd = zbpdf.substring(0, zbpdf.lastIndexOf(File.separator) + 1) + "评审规则表.md";
		String ruleresult = "";
		if (!new File(rulemd).exists()) {
			// 取出招标文件原文
			String zbpdfContent = null;
			try {
				zbpdfContent = PDFConvertStringUtil.parseByWordCom(new FileInputStream(zbpdf));
			}
			catch (FileNotFoundException e) {
				throw new RuntimeException(e);
			}
			
			StringBuilder rules = new StringBuilder();
			rules.append("请解析下招标要求信息，招标文件内容如下：").append("\n");
			rules.append(zbpdfContent).append("\n");
			rules.append("评审规则表如下:").append("\n");
			rules.append(zbyqToStrip());
			try (FileWriter w = new FileWriter(rulemd.replace(".md", ".txt"))) {
				w.write(rules.toString());
				w.flush();
			} catch (IOException e) {
				e.printStackTrace();
			}
			ruleresult = VolcengineAiUtils.chatForRequireAgent(rules.toString()).getData();
			System.out.println("评审规则提取：" + (System.currentTimeMillis() - t));
			System.out.println("输出长度：" + ruleresult.length());
			// 保存评审规则表md
			try (FileWriter w = new FileWriter(rulemd)) {
				w.write(ruleresult);
				w.flush();
			} catch (IOException e) {
				e.printStackTrace();
			}
		} else {
			try (BufferedReader reader = new BufferedReader(new FileReader(rulemd))) {
				String line;
				while ((line = reader.readLine()) != null) {
					ruleresult += line;
					ruleresult += "\n";
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

		// markdown整理出AI评审规则提示词 可以作为system消息
		StringBuilder psContent = new StringBuilder();
		psContent.append("评审规则表如下:\n");
		psContent.append(ruleresult);
		psContent.append("\n");
		// 明确输出规范
		psContent.append("请开始评审。");
		// pdf转图片base64
		List<String> imgUrls = new ArrayList<>();
		File[] fss = new File(tbpdfdir).listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if (pathname.isDirectory())
					return false;
				else
					return true;
			}
		});
		for (File ff : fss) {
			if (ff.getName().endsWith(".pdf")) {
				String pdfdir = tbpdfdir+"\\" + ff.getName().replace(".pdf", "");
				File pdfdirfile = new File(pdfdir);
				if (!pdfdirfile.exists()) {
					pdfdirfile.mkdir();
					try (PDDocument document = PDDocument.load(ff)) {
						for (int index = 0; index < document.getNumberOfPages(); index++) {
							BufferedImage image = Utils.pageConvertToImage(document, document.getPage(index), 90,
									ImageType.RGB);
							File img = new File(pdfdir + File.separator + (index + 1) + ".png");
							ImageIO.write(image, "png", img);
							imgUrls.add(ImageConvertBase64.toBase64(img, true));
						}
					} catch (IOException e1) {
						e1.printStackTrace();
					}
				} else {
					File[] fs = pdfdirfile.listFiles();
					Arrays.sort(fs, new Comparator<File>() {
			            @Override
			            public int compare(File f1, File f2) {
			                // 提取文件名中的数字部分进行比较
			                String name1 = f1.getName();
			                String name2 = f2.getName();
			                // 使用正则表达式匹配文件名中的数字部分
			                String num1 = name1.replaceAll("\\D", "");
			                String num2 = name2.replaceAll("\\D", "");
			                int num1Int = num1.isEmpty() ? 0 : Integer.parseInt(num1);
			                int num2Int = num2.isEmpty() ? 0 : Integer.parseInt(num2);
			                // 先按数字从小到大排序
			                return Integer.compare(num1Int, num2Int);
			            }
			        });
					for (File img : fs) {
						imgUrls.add(ImageConvertBase64.toBase64(img, true));
					}
				}
			} else if (ff.getName().endsWith(".png") || ff.getName().endsWith(".jpg")
					|| ff.getName().endsWith(".jpeg")) {
				imgUrls.add(ImageConvertBase64.toBase64(ff, true));
			}
		}

		// AI进行评审
		String result = VolcengineAiUtils.chatForBidAgent_cg(psContent.toString(), imgUrls.stream().limit(20).collect(Collectors.toList()),
				"补充了部分投标文件材料，提取完善“投标响应情况”中缺失的关键信息后再次评审。").getData();
		System.out.println(result);
		System.out.println("****长度"+result.length());
		if (StringUtil.isBlank(result)) {
			return;
		}
		// 保存评审结果md
		try (FileWriter w = new FileWriter(tbpdfdir + "评审结果.md")) {
			w.write(result);
			w.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}

		// 关闭大模型sdk
		VolcengineAiUtils.close();

		System.out.println("总耗时：" + (System.currentTimeMillis() - t));
	}

	@Test
	public void testQwen(){
		long t = System.currentTimeMillis();
		String zbpdf = "D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\模型比对\\2B3206010318000551001002南通火车东站综合客运枢纽工程施工\\招标文件正文.pdf";
		String tbpdfdir = "D:\\desktopfile\\SE相关\\技术架构\\ai大模型\\模型比对\\2B3206010318000551001002南通火车东站综合客运枢纽工程施工\\投标文件\\安徽省中瀚建设工程有限公司";

		// 初始化大模型sdk
		VolcengineAiUtils.init("");

		// 评审规则表组装，提取招标要求
		String rulemd = zbpdf.substring(0, zbpdf.lastIndexOf(File.separator) + 1) + "评审规则表.md";
		String ruleresult = "";
		if (!new File(rulemd).exists()) {
			// 取出招标文件原文
			String zbpdfContent = null;
			try {
				zbpdfContent = PDFConvertStringUtil.parseByWordCom(new FileInputStream(zbpdf));
			}
			catch (FileNotFoundException e) {
				throw new RuntimeException(e);
			}

			StringBuilder rules = new StringBuilder();
			rules.append("请解析下招标要求信息，招标文件内容如下：").append("\n");
			rules.append(zbpdfContent).append("\n");
			rules.append("评审规则表如下:").append("\n");
			rules.append(zbyqToStrip());
			try (FileWriter w = new FileWriter(rulemd.replace(".md", ".txt"))) {
				w.write(rules.toString());
				w.flush();
			} catch (IOException e) {
				e.printStackTrace();
			}
			ruleresult = VolcengineAiUtils.chatForRequireAgent(rules.toString()).getData();
			System.out.println("评审规则提取：" + (System.currentTimeMillis() - t));
			System.out.println("输出长度：" + ruleresult.length());
			// 保存评审规则表md
			try (FileWriter w = new FileWriter(rulemd)) {
				w.write(ruleresult);
				w.flush();
			} catch (IOException e) {
				e.printStackTrace();
			}
		} else {
			try (BufferedReader reader = new BufferedReader(new FileReader(rulemd))) {
				String line;
				while ((line = reader.readLine()) != null) {
					ruleresult += line;
					ruleresult += "\n";
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

		// markdown整理出AI评审规则提示词 可以作为system消息
		StringBuilder psContent = new StringBuilder();
		psContent.append("评审规则表如下:\n");
		psContent.append(ruleresult);
		psContent.append("\n");
		// 明确输出规范
		psContent.append("请开始评审。");
		// pdf转图片base64
		List<String> imgUrls = new ArrayList<>();
		File[] fss = new File(tbpdfdir).listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if (pathname.isDirectory())
					return false;
				else
					return true;
			}
		});
		for (File ff : fss) {
			if (ff.getName().endsWith(".pdf")) {
				String pdfdir = tbpdfdir+"\\" + ff.getName().replace(".pdf", "");
				File pdfdirfile = new File(pdfdir);
				if (!pdfdirfile.exists()) {
					pdfdirfile.mkdir();
					try (PDDocument document = PDDocument.load(ff)) {
						for (int index = 0; index < document.getNumberOfPages(); index++) {
							BufferedImage image = Utils.pageConvertToImage(document, document.getPage(index), 90,
									ImageType.RGB);
							File img = new File(pdfdir + File.separator + (index + 1) + ".png");
							ImageIO.write(image, "png", img);
							imgUrls.add(img.toString());
						}
					} catch (IOException e1) {
						e1.printStackTrace();
					}
				} else {
					File[] fs = pdfdirfile.listFiles();
					Arrays.sort(fs, new Comparator<File>() {
						@Override
						public int compare(File f1, File f2) {
							// 提取文件名中的数字部分进行比较
							String name1 = f1.getName();
							String name2 = f2.getName();
							// 使用正则表达式匹配文件名中的数字部分
							String num1 = name1.replaceAll("\\D", "");
							String num2 = name2.replaceAll("\\D", "");
							int num1Int = num1.isEmpty() ? 0 : Integer.parseInt(num1);
							int num2Int = num2.isEmpty() ? 0 : Integer.parseInt(num2);
							// 先按数字从小到大排序
							return Integer.compare(num1Int, num2Int);
						}
					});
					for (File img : fs) {
						imgUrls.add(img.toString());
					}
				}
			} else if (ff.getName().endsWith(".png") || ff.getName().endsWith(".jpg")
					|| ff.getName().endsWith(".jpeg")) {
				imgUrls.add(ff.toString());
			}
		}

		String system="# 角色\n" +
				"你是一位资深的建筑工程资格审查专家，熟悉建筑工程专业知识，能解读招投标文件后按评审规则给出准确的评审结论。\n" +
				"# 任务描述与要求\n" +
				"1. 仔细研读用户提供的招标文件内容、投标文件材料、评审规则表，基于评审规则表的“审查点”“评审要求”提取投标材料中的“投标响应情况”信息填写，信息应具体详细。（只填写投标材料中找到的相关内容，不能做出假设猜想，投标材料没有可提取的信息则将上轮评审结果返回。）\n" +
				"2. 依据评审规则表的“AI评审规则”，逐条评审“投标响应情况”是否符合“评审要求”“招标文件规定”的要求，给出准确清晰的“AI评审结论”，在“AI评审理由”写出你的评审理由。（若投标响应情况缺失评审所需关键信息的或招标要求不明的，结论给定为“提请人工判断”）\n" +
				"# 相关限制\n" +
				"1.评审仅基于用户提供的投标材料信息和上轮评审结果。\n" +
				"2.如果存在上轮评审结果，逐条检查“审查点”的“评审结论”，若结论是“合格”的无需评审，若是“不合格”和“提请人工判断”的需要根据补充的投标文件材料完善“投标响应情况”后再次评审，给出新的“AI评审结论”并完善评审理由。\n" +
				"3.如果存在上轮评审结果，但投标材料没有可提取的信息则将上轮评审结果返回。\n" +
				"# 输出格式要求\n" +
				"必须以Markdown表格格式返回评审结果表，评审结果表不需要“AI评审规则”列。（如果没有评审所需的材料则将上轮评审结果或原评审规则表输出）";
		// AI进行评审
		String result="";
		for (int i = 0; i < ((imgUrls.size() - 1) / QwenAiUtils.IMAGELIMITNUM_MODEL) + 1; i++) {
			if(StringUtil.isBlank(result)){
				result="上轮评审结果：" +result;
			}
			List<String> inputImgs =//imgUrls;
					imgUrls.stream().skip(i * QwenAiUtils.IMAGELIMITNUM_MODEL).limit(QwenAiUtils.IMAGELIMITNUM_MODEL).collect(Collectors.toList());
			result = QwenAiUtils.callAndLogtime(logger, "千问单轮评审耗时", r -> QwenAiUtils.baseChatWithModel_Img(system,
					psContent.toString(), QwenAiUtils.MODEL_QWEN_25VL_32B,
					r, inputImgs, "补充了部分投标文件材料，提取完善“投标响应情况”中缺失的关键信息后再次评审。"), result).getData();


		}
//		String result = VolcengineAiUtils.chatForBidAgent_cg(psContent.toString(), imgUrls,
//				"补充了部分投标文件材料，提取完善“投标响应情况”中缺失的关键信息后再次评审。").getData();
		System.out.println(result);
		System.out.println("****长度"+result.length());
		if (StringUtil.isBlank(result)) {
			return;
		}
		// 保存评审结果md
		try (FileWriter w = new FileWriter(tbpdfdir + "评审结果_Qwen.md")) {
			w.write(result);
			w.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}

		// 关闭大模型sdk
		VolcengineAiUtils.close();

		System.out.println("总耗时：" + (System.currentTimeMillis() - t));
	}

	private static List<Map<String, String>> md2List(String content) {
		List<Map<String, String>> tableData = new ArrayList<>();
		List<String> headers = new ArrayList<>();
		Map<String, String> rowData = null;
		int cols = 0;
		int index = 0;
		String regex = "\\|(.*?)\\|";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(content);
		boolean sp = false;
		int start = 0;
		while (matcher.find(start)) {
			String txt = matcher.group(1);
			start = matcher.end() - 1;
			if (txt.contains("\n")) {
				continue;
			}
			txt = txt.trim();
			if (txt.startsWith("-") && txt.endsWith("-")) {
				cols++;
				sp = true;
				continue;
			}
			txt = txt.replaceAll("<br/>", "");
			if (!sp) {
				headers.add(txt);
			} else {
				if (rowData == null) {
					rowData = new HashMap<>();
				}
				rowData.put(headers.get(index % cols), txt);
				if ((index + 1) % cols == 0) {
					tableData.add(rowData);
					rowData = null;
				}
				index++;
			}
		}
		return tableData;
	}

	protected String zbyqToStrip(){
		return "| 序号 | 审查点 | 评审要求 | 招标文件规定 | 投标响应情况 | AI评审规则 | AI评审理由 | AI评审结论 |\n" +
				"| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |\n" +
				"| 1 | 投标人名称评审 | 与营业执照、资质证书、安全生产许可证一致 |  | 投标函中投标人名称为【N4】，营业执照上载明的名称为【N1】，资质证书上载明的名称为【N2】，安全生产许可证上载明的名称为【N3】 | 设【投标文件】中投标企业的“营业执照”、“资质证书”、“安全生产许可证”三种证书中载明的“投标企业名称”分别是N1、N2、N3，【投标文件】的“投标函”中载明的投标企业的名称为N4。若N4=N1=N2=N3，则投标人名称有效，投标人入围。N4、N3、N2、N1中，有任意两个名称不一致的，投标人名称无效，投标人初步评审不合格。 |  | 合格/不合格/提请人工判断 |\n" +
				"| 2 | 投标函签字盖章评审 | 有法定代表人的电子签章并加盖法人电子印章 |  | 投标函中【有/无】“法定代表人的电子签章”，具体信息为：【】，“投标单位的电子印章”具体信息为：【】 | 分析“投标函”中是否包含投标企业法定代表人的电子签章、投标单位的电子印章。若“投标函”中缺少“投标企业法定代表人的电子签章”或者“投标单位的电子印章”任意一个的，投标无效，初步评审不合格。同时具有“投标企业法定代表人的电子签章”和“投标单位的电子印章”的，初步评审合格。 |  | 合格/不合格/提请人工判断 |\n" +
				"| 3 | 报价唯一性评审 | 报价唯一，只能有一个有效报价 |  | 投标函中【有/无】唯一的报价表述，报价为【】元                  | 检验“投标函”中投标报价的表述是否“有”且“仅有一处”。若“投标函”中的报价存在两个及两个以上表述的，则报价唯一性评审不合格。 |  | 合格/不合格/提请人工判断 |\n" +
				"| 4 | 营业证照评审 | 具备有效的营业执照 | 开标时间：【】，本项目的专业属性信息为：【】 | “投标函”中的企业名称为【N2】，“营业执照”中的“企业名称”为【N1】，“投标函”中的“法定代表人”名称为【R2】，“营业执照”中的“法定代表人”为【R1】，“营业执照”中的“营业期限”，为【L1】，“营业执照”中的“经营范围”为【M1】 | 分别对“营业执照”中的“企业名称”、“法定代表人”、“营业期限”和“经营范围”等四个要素逐项进行审查。（1）获取“营业执照”中的“企业名称”（设“企业名称”为N1）与【投标文件】的“投标函”中的企业名称（设“投标函”中的企业名称为N2）是否相同。相同的，即N2=N1的，为合格。否则，为不合格。（2）获取“营业执照”中的“法定代表人”（设“法定代表人”为R1）与【投标文件】的“投标函”中“法定代表人”的名称（设“投标函”中的“法定代表人”名称为R2）是否相同。相同的，即R2=R1的，为合格。否则，为不合格。（3）获取“营业执照”中的“营业期限”，设“营业期限”为L1。再获取【招标文件】中本项目开标时间（精确到年月日），设为L2，如果L1≥L2，即“营业执照”中的“营业期限”超过本项目开标时间的，该“营业期限”有效，否则为无效，评审为不合格。（4）获取“营业执照”中的“经营范围”信息，设经营范围为M1。获取【招标文件】中本项目的专业属性信息，设为M2，如果M2属于M1的子集，该经营范围有效，否则为无效，评审为不合格。以上四个要素检查全部合格的，营业执照评审结论为有效，否则为无效。 |  | 合格/不合格/提请人工判断 |\n" +
				"| 5 | 安全生产许可证有效性评审 | 具备有效的安全生产许可证 | 开标时间：【】，本项目的专业属性信息为：【】 | 投标函中的企业名称为【N2】，安全生产许可证中的企业名称为【N1】，安全生产许可证中的有效期限为【L1】，安全生产许可证中的许可范围为【M1】 | 逐项审查要素：（1）企业名称有效性：获取“安全生产许可证”中的“单位名称”（设为N1）与投标文件的“投标函”中的企业名称（设为N2）是否相同。相同的，即N2=N1，为合格。否则，为不合格。（2）有效期限：获取“安全生产许可证”中的“有效期”，设为L1。再获取招标文件中本项目开标时间（精确到年月日），设为L2，如果L1≥L2，即“安全生产许可证”中的“有效期限”超过本项目开标时间，该“有效期限”有效，否则为无效，评审为不合格。（3）许可范围：获取“安全生产许可证”中的“许可范围”信息，设为M1。获取招标文件中本项目的专业属性信息，设为M2，如果M2属于M1的子集，该许可范围有效，否则为无效，评审为不合格。 |  | 合格/不合格/提请人工判断 |\n" +
				"| 6 | 资质证书有效性评审 | 具备有效的符合招标文件要求的资质证书 | 开标时间：【】，资质类别要求为【】，资质等级要求为【】。 | 投标函中的企业名称为【N2】，资质证书中的企业名称为【N1】。投标函中的法定代表人名称为【R2】，资质证书中的法定代表人为【R1】，资质证书中的有效期限为【L1】，资质证书中的资质类别为【C1】，资质等级为【Z1】 | 逐项审查要素：（1）企业名称有效性：获取“资质证书”中的“企业名称”（设为N1）与投标文件的“投标函”中的企业名称（设为N2）是否相同。相同的，即N2=N1，为合格。否则，为不合格。（2）法定代表人有效性：获取“资质证书”中的“法定代表人”（设为R1）与投标文件的“投标函”中“法定代表人”的名称（设为R2）是否相同。相同的，即R2=R1，为合格。否则，为不合格。（3）有效期限：获取“资质证书”中的“有效期限”，设为L1。再获取招标文件中本项目开标时间（精确到年月日），设为L2，如果L1≥L2，即“资质证书”中的“有效期限”超过本项目开标时间，该“有效期限”有效，否则为无效，评审为不合格。（3）资质等级及类别（专业）：获取“资质证书”中的“资质类别”（设为C1）和“资质等级”（设为Z1）信息。获取【招标文件】中投标单位需要具备的“资质类别”（设为C2）和“资质等级”（设为Z2），如果C1包含或等于C2，且L1等级高于或等于L2，为有效，评审为合格。否则为无效，评审为不合格。 |  | 合格/不合格/提请人工判断 |\n" +
				"| 7 | 企业业绩有效性评审 | 投标人自XXXX年 XX月XX日（以“交（竣）工验收证明”上的验收日期为准），承担过单项合同金额不低于XX万元XXXX的类似工程。 | 本项目的企业业绩要求为：【】 | 投标文件中“类似项目情况表”中业绩存在【】条，分别是【】，（项目名称，竣工日期，合同金额，类似工程描述） | 获取【投标文件】中提供的类似工程业绩材料，包括中标通知书、施工合同、竣工备案表（证书）、竣工验收证明等。提取并解析类似工程业绩材料中的关键信息，判断其是否属于类似工程。通常“类似工程”是指：项目在规模、面积、造价、层次、跨度、结构形式、施工工艺、特殊施工技术等方面与招标工程相类似。提取符合条件的类似工程业绩是否满足时限和金额的要求。如招标文件规定，近5年来有过类似工程业绩，是指从开标当日起计算，向前推算5年的业绩。 | | 合格/不合格/提请人工判断 |\n" +
				"| 8 | 拟派项目负责人有效性评审 | 具有XXX专业XXX级别及以上注册建造师，同时具有安全生产考核合格证（B证），符合第二章“投标人须知”规定 | 本项目拟派项目负责人建造师注册专业为【】，等级为【】，开标时间：【】 | 投标文件中主要人员简历表中项目负责人（等同于项目经理）姓名为【】，建造师证书中姓名为【】，注册专业为【】，等级为【】。投标函中的企业名称为【】,安全生产考核合格证（B证）中的企业名称为【】，姓名为【】，有效期限为【】。项目负责人的劳动合同书【有/无】劳动者的签名（签章）【有/无】用人单位的公章或者法定代表人的签名（签章)，劳动合同书中载明的劳动者的服务期限为【】，养老保险证明中【有/无】项目负责人姓名。投标人“法定代表人”为【】、“公司董事长”为【】、“总经理”为【】。项目负责人【是/不是】一级建造师，【需要/无需】电子注册证书，电子注册证书中【有/无】提供电子证书，【有/无】手写签名。 | 逐项审查要素：（1）【招标文件】规定项目负责人的建造师专业和等级分别为S1和C1，【投标文件】提供的拟派项目负责人的建造师专业和等级为S2和C2。若同时满足：S1![img](file:///C:\\Users\\<USER>\\AppData\\Local\\Temp\\ksohtml17392\\wps1.png)S2（即S2专业包含或等于S1）且C2的等级高于C1，则专业和等级评审结果为有效，否则为无效。（2）分析检查【投标文件】中项目负责人的安全生产考核合格证（B证）是否有效，即检查其B证是否为项目负责人本人、是否处于有效期内(即证书有效日期在项目开标当日仍然有效)、企业名称是否与“投标函”中的投标企业名称相一致。全部检查合格的，安全生产考核证评审合格，否则为不合格。（3)分析检查【投标文件】中项目负责人与用人单位（即投标人)签订的劳动用工合同是否有效。有效的要求有3点：1、劳动合同书是否有劳动者的签名（签章）和用人单位的公章或者法定代表人的签名（签章)；具备的为有效，否则为无效；2、劳动合同书中载明的劳动者的服务期限是否超过本次项目的开标截止时间；超过的为有效，否则为无效；3、如果【投标文件】提供了养老保险证明材料的，视为劳动合同有效。（4)获取【投标文件】中“投标函”、“营业执照”、“资质证书”、“公司章程”等文件中投标人“法定代表人”、“公司董事长”、“总经理”等人员名称信息；如果“法定代表人”、“公司董事长”、“总经理”与拟派“项目负责人”名字相同的，属于违反招标文件中项目负责人不得为公司“法定代表人”、“公司董事长”、“总经理”的规定，判定为不合格，否则为合格。（5)如果拟派项目负责人为一级注册建造师的，要获取【投标文件】中项目负责人的电子注册证书，判断其是否在个人签名处手写本人签名，未提供电子证书或未手写签名或签名图像笔迹不一致的，该电子证书无效，视为项目负责人的资格不符合要求，判定为不合格。 | | 合格/不合格/提请人工判断 |\n" +
				"| 9 | 诚信承诺书有效性评审 | 经投标人盖章及法定代表人签字或盖章的承诺书（按照【招标文件】提供“诚信承诺书”格式填写） | 招标文件中规定的“诚信承诺书”内容：【】 | 投标文件中“诚信承诺书”内容：【】 | 逐项核对以下信息：（1）投标人【投标文件】中提供的“诚信承诺书”格式与【招标文件】规定的“诚信承诺书”格式是否一致；格式一致性包含以下几个方面：段落数相同，基本意思表达相同，对于关键字句没有修改或者歪曲的意思表达。一致的为合格，不一致为不合格；（2）投标人【投标文件】中提供的“诚信承诺书”有没有投标单位盖章和法定代表人签字，或者有盖章的承诺书；齐全的为合格，不齐全的为不合格。（3）投标人【投标文件】中提供的“诚信承诺书”致函的对象是否为拟投标项目的招标人，中拟派的项目负责人是否为“投标函”中明确的项目负责人，一致的为合格，不一致的为不合格。 | | 合格/不合格/提请人工判断 |\n" +
				"| 10 | 投标内容响应性评审 | 符合【招标文件】“投标人须知前附表”中“招标范围”规定的内容 |  | 投标文件的“施工组织设计”或者“投标函”等文件中【包含/未包含】“招标范围”规定内容 | 按照【招标文件】中“招标范围”规定的招标内容，仔细检查【投标文件】的“施工组织设计”或者“投标函”等文件中是否包含“招标范围”规定内容，包含的为有效响应，未包含的，为无效响应。 | | 合格/不合格/提请人工判断 |\n" +
				"| 11 | 工期响应性评审 | 投标函中载明的工期符合“投标人须知” 中关于工期的要求 | 招标文件中关于“工期”的具体要求是【】 | 投标文件的“投标函”中关于“工期”的承诺是【】 | 获取【招标文件】中规定的“计划开工时间”，设该时间为K1，获取【招标文件】中规定的“计划竣工时间”，设该时间为J1，获取【招标文件】中规定的“工期”，设该时间为L1；获取【投标文件】中投标人承诺的“计划开工时间”，设该时间为K2，获取【投标文件】中投标人承诺的“计划竣工时间”，设该时间为J2，获取【投标文件】中承诺完成的“工期”，设该时间为L2。 | | 合格/不合格/提请人工判断 |\n" +
				"| 12 | 工程质量响应性评审 | 投标函中载明的质量承诺满足【招标文件】中“投标人须知” 关于“质量要求”或者“质量标准”的规定 | 工程质量标准规定是【】 | 投标文件的“投标函”中关于的“质量标准”的承诺是【】 | 获取【招标文件】中规定的“质量标准”，设该标准为Q1，获取【投标文件】中投标人承诺的“质量标准”，设该标准为Q2，若Q2未能满足Q1的实质性要求和条件，则本次投标无效，投标人不得入围。 若Q2满足Q1的实质性要求和条件，则本次投标有效，投标人入围。 | | 合格/不合格/提请人工判断 |\n" +
				"| 13 | 投标有效期响应性评审 | “投标函”附录中承诺的投标有效期满足【招标文件】“投标人须知”前附表中“投标有效期”的规定 | “投标人须知”前附表中“投标有效期”的规定是【】 | “投标函”附录中承诺的“投标有效期”是【】 | 提取【招标文件】“投标人须知”前附表中“投标有效期”的规定，设该有效期为L1，提取【投标文件】“投标函”附录中承诺的“投标有效期”，设该有效期为L2，比较L1和L2，若L2≥L1时，为有效，评审合格，否则评审为不合格。 | | 合格/不合格/提请人工判断 |\n" +
				"| 14 | 投标保证金响应性评审 | 符合第二章“投标人须知”规定 | “投标人须知”前附表以及“投标保证金”方面的规定和要求是【】 | 投标人“投标函”或者其他方面关于“投标保证金”的承诺是【】 | 提取【招标文件】中“投标人前附表”以及“投标保证金”方面的规定和要求；提取【投标文件】中投标人“投标函”或者其他方面关于“投标保证金”的承诺；比较【投标文件】中投标人关于“投标保证金”的承诺以及【招标文件】中“投标人前附表”以及“投标保证金”方面的规定和要求，投标人实质性响应【招标文件】要求按照招标文件要求递交了投标保证金的，为有效，投标保证金响应性合格，否则为不合格。 | | 合格/不合格/提请人工判断 |\n" +
				"| 15 | 工程量清单响应性性 | （1）“投标报价”不低于工程成本或者不高于【招标文件】设定的“招标控制价”或者不高于【招标文件】设定的“招标人期望值”；（2" +
				"）未改变【招标文件】中“工程量清单”给出的项目编码、项目名称、项目特征、计量单位和工程量；（3）未改变【招标文件】规定的“暂估价”、“暂列金额”及“甲供材料价格”；（4）未改变“不可竞争费用”项目或“费率”或“计算基础”。 | “投标人须知”前附表中“招标控制价”是【】，招标文件中规定的：项目编码、项目名称、项目特征、计量单位和工程量、暂估价、暂列金额、甲供材料价格、不可竞争费用项目、费率或计算基础有【】 | 投标人“投标函”中“”投标报价“是【】；“已标价的工程量清单”中的：项目编码、项目名称、项目特征、计量单位和工程量、暂估价、暂列金额、甲供材料价格、不可竞争费用项目、费率或计算基础有【】 | 投标人未改变工程量的规定格式和内容的合格，否则为不合格。 | | 合格/不合格/提请人工判断 |\n";
	}
}
