package com.epoint.ebpu.AIClientSDK.client;

import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.dashscope.app.ApplicationUsage;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.MultiModalMessage;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.protocol.ConnectionConfigurations;
import com.alibaba.dashscope.utils.Constants;
import com.alibaba.fastjson.JSONObject;
import com.epoint.ebpu.AIClientSDK.client.function.RetrySupplier;
import com.epoint.ebpu.AIClientSDK.client.vo.MultiTalkVO;
import com.epoint.ebpu.AIClientSDK.client.vo.ResultVO;
import com.epoint.ebpu.AIClientSDK.util.JsonUtil;
import com.epoint.ebpu.AIClientSDK.util.MarkdownUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.StopWatch;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Title 千问大模型调用
 * <AUTHOR>
 * @Date 2025-03-14
 * @Version
 * @Description
 */
public class QwenAiUtils {
    protected static final org.slf4j.Logger logger = LoggerFactory.getLogger(QwenAiUtils.class);

    private static final String APIKEY = System.getProperty("model_apiKey");

    /**
     * 单轮输入的图片数量(模型下)
     */
    public static final int IMAGELIMITNUM_MODEL = 20;

    /**
     * 单轮输入的图片数量（应用下）
     */
    public static final int IMAGELIMITNUM_APP = 50;

    /**
     * 超时时重试次数
     */
    public static final int TIMEOUT_RETRYCNT=1;

    static {
        //常量设置
        Constants.connectionConfigurations = ConnectionConfigurations.builder()
                .connectTimeout(Duration.ofSeconds(600))  // 建立连接的超时时间, 默认 120s
                .readTimeout(Duration.ofSeconds(300)) // 读取数据的超时时间, 默认 300s
                .writeTimeout(Duration.ofSeconds(60)) // 写入数据的超时时间, 默认 60s
                .connectionIdleTimeout(Duration.ofSeconds(300)) // 连接池中空闲连接的超时时间, 默认 300s
                .connectionPoolSize(32) // 连接池中的最大连接数, 默认 32
                .maximumAsyncRequests(32)  // 最大并发请求数, 默认 32
                .maximumAsyncRequestsPerHost(32) // 单个主机的最大并发请求数, 默认 32
                .responseTimeout(Duration.ofSeconds(600))
                .build();
    }

    /************************** bot信息部分 ***********************/

    /**
     * 评标办法提取生成应用的ID
     * [招标办法提取]
     */
    private static final String PBBFSTRIPPER_BOTAPPID=System.getProperty("pbbfstripper_botappid");

    /**
     * 评标办法提取招标要求生成应用的ID
     * [评标办法提取招标要求]
     */
    private static final String PBBFREQUIREMENTSTRIPPER_BOTAPPID=System.getProperty("pbbfrequirementstripper_botappid");

    /**
     * 评审规则生成应用的ID
     * [评审规则生成]
     */
    private static final String ASSESSRULE_BOTAPPID=System.getProperty("assessrule_botappid");

    /**
     * 招标项目信息提取应用的ID
     * [招标项目信息提取]
     */
    private static final String BIDFILEDATASTRIPPER_BOTAPPID=System.getProperty("bidfiledatastripper_botappid");

    /**
     * AI智能评标应用的ID
     * [AI智能评标]
     */
    public static final String AIEVALUATION_BOTAPPID=System.getProperty("aievaluation_botappid");

    /**
     * 单评分点单位横向评审应用的ID
     * [单评分点单位横向评审]
     */
    public static final String HORIZONTALEVAL_BOTAPPID=System.getProperty("horizontaleval_botappid");


//        String modelValue="qwen-vl-plus-latest";//这个效果差
//        String modelValue="qwen-vl-max-latest";
//        String modelValue="qwen2.5-vl-72b-instruct";
    public static final String MODEL_QWEN_25VL_32B="qwen2.5-vl-32b-instruct";

    public static final String MODEL_QWEN_25_32B="qwen2.5-32b-instruct";

    /************************** 一些固定提示词 ***********************/

    /**
     * 询问AI是否输出完成提示词
     */
    public static final String PROMPT_ISEND="继续说。若回复内容已全部结束，则返回’TALKEND‘;" +
            "若继续说，请紧接上一轮的最后20个字输出（从这20个字的下一个字输出），上一轮回复最后20个字为:%s";


    public static final String PROMPT_TIMEOUTRETRY="会话刚才超时了，请继续思考，输出问题的回答";
    /************************** 调用应用的方法 ***********************/

    /**
     * 从招标文件中提取评标办法
     *
     * @param question
     * @return
     */
    public static MultiTalkVO chatWithBot_PbbfStrip(String question){
        MultiTalkVO callResult = callAndLogtime(logger, "招标文件中提取评标办法", r -> QwenAiUtils.chatWithBot_MultiOutput(r,
                PBBFSTRIPPER_BOTAPPID), question);
        return callResult;
    }

    /**
     * 评标办法提取招标要求
     *
     * @param question
     * @return
     */
    public static MultiTalkVO chatWithBot_RequirementStrip(String question){
        MultiTalkVO callResult = callAndLogtime(logger, "评标办法提取招标要求", r -> QwenAiUtils.chatWithBot_MultiOutput(r,
                PBBFREQUIREMENTSTRIPPER_BOTAPPID), question);
        return callResult;
    }

    /**
     * 评审规则生成
     *
     * @param question
     * @return
     */
    public static MultiTalkVO chatWithBot_AssessRule(String question){
        MultiTalkVO callResult = callAndLogtime(logger, "评审规则生成", r -> QwenAiUtils.chatWithBot_Simple(r, ASSESSRULE_BOTAPPID), question);
        return callResult;
    }

    /**
     * 招标项目信息提取
     *
     * @param question
     * @return
     */
    public static MultiTalkVO chatWithBot_BidFileDataStripper(String question){
        MultiTalkVO callResult = callAndLogtime(logger, "招标项目信息提取", r -> QwenAiUtils.chatWithBot_Simple(r, BIDFILEDATASTRIPPER_BOTAPPID), question);
        return callResult;
    }

    /**
     * AI智能评审
     *
     * @param question
     * @return
     */
    public static MultiTalkVO chatWithBot_AiEvaluation(String question,List<String> imgs){
        MultiTalkVO callResult = callAndLogtime(logger, "AI智能评审",
                r -> QwenAiUtils.baseChatWithBot(r, AIEVALUATION_BOTAPPID,"",imgs,true),
                question);
        return callResult;
    }

    /**
     * 单评分点单位横向评审
     *
     * @param question
     * @return
     */
    public static MultiTalkVO chatWithBot_HorizontalEval(String question){
        MultiTalkVO callResult = callAndLogtime(logger, "单位横向评审",
                r -> QwenAiUtils.chatWithBot_Simple(r, HORIZONTALEVAL_BOTAPPID),question);
        return callResult;
    }

    /************************** 调用应用的中间方法 ***********************/

    /**
     * 通过千问应用AI获取结果
     *
     * @param question 问题
     * @param botAppId 应用的appid
     * @return
     */
    public static MultiTalkVO chatWithBot_Simple(String question, String botAppId) {
        return baseChatWithBot(question,botAppId,"",new ArrayList<>(),false);
    }

    /**
     * 通过千问应用AI获取结果，多轮直到全部输出完,适用于长内容输出
     *
     * @param question
     * @param botAppId
     * @return
     */
    public static MultiTalkVO chatWithBot_MultiOutput(String question, String botAppId) {
        return baseChatWithBot(question,botAppId,"",new ArrayList<>(),true);
    }

    /**
     * 图文理解调用，输入图片会分多轮
     * 外部需使用静态内部类QwenCaller来进行调用
     *
     * @param question
     * @param botAppId
     * @param sessionId
     * @param imgs 图片此方法中目前不支持
     * @param isMultiOutput
     * @return
     */
    protected static MultiTalkVO baseChatWithBot(String question, String botAppId,String sessionId,List<String> imgs,
                                              boolean isMultiOutput) {
        MultiTalkVO multiTalkVO = new MultiTalkVO();
        ApplicationParam.ApplicationParamBuilder<?, ?> prompt = ApplicationParam.builder()
                .apiKey(APIKEY)
                .appId(botAppId);
        //有sessionId则保持会话
        if(StringUtils.isNotBlank(sessionId)){
            prompt.sessionId(sessionId);
        }
        ApplicationParam param = prompt
                .build();

        Application application = new Application();
        List<String> details=new ArrayList<>();
        try {
            //塞入图片，有图片先输入图片内容
            if(!imgs.isEmpty()){
                for (int i = 0; i < (imgs.size()-1) / IMAGELIMITNUM_APP+1; i++) {
                    List<String> inputImgs =
                            imgs.stream().skip(i * IMAGELIMITNUM_APP).limit(IMAGELIMITNUM_APP).collect(Collectors.toList());
                    param.setImages(inputImgs);
                    param.setPrompt(String.format("此轮对话为设置图片内容信息，待后面给出问题后分析、回答问题。图片上传进度（%s/%s）。",
                            i+1, (imgs.size()-1) / IMAGELIMITNUM_APP+1));
                    ApplicationResult result = application.call(param);
                    param.setSessionId(result.getOutput().getSessionId());
                    logger.info("imgs第{}轮，sessionid:{}，token信息:{},输出：\r\n{} ",i+1,result.getOutput().getSessionId(),
                            JSONObject.toJSON(result.getUsage().getModels()).toString(),
                            result.getOutput().getText());
                    //统计token
                    for (ApplicationUsage.ModelUsage model : result.getUsage().getModels()) {
                        multiTalkVO.setInputTokens(multiTalkVO.getInputTokens()+model.getInputTokens());
                        multiTalkVO.setOutputTokens(multiTalkVO.getOutputTokens()+model.getOutputTokens());
                    }
                }
            }

            //回答、最多10轮对话
            for (int i = 0; i < 10; i++) {
                if(i==0) {
                    //第0轮塞入用户提示词
                    param.setPrompt(question);
                }
                ApplicationResult result = RetrySupplier.callAndRetryWhenTimeOut_Qwen(
                                () -> application.call(param),
                                () -> {
                                    //有sessionid时，说明会话已经存在了，才更换提示词，直接要结果
                                    if(StringUtils.isNotBlank(param.getSessionId())) {
                                        param.setPrompt(PROMPT_TIMEOUTRETRY);
                                    }
                                    return application.call(param);
                                }, logger, TIMEOUT_RETRYCNT);
                logger.info("第{}轮，sessionid:{}，token信息:{},输出：\r\n{}： ",i+1,result.getOutput().getSessionId(),
                        JSONObject.toJSON(result.getUsage().getModels()).toString(),
                        result.getOutput().getText());

                param.setSessionId(result.getOutput().getSessionId());
                //统计token，会话中大模型返回的是累加的，因此只要获取最后的就行了
                for (ApplicationUsage.ModelUsage model : result.getUsage().getModels()) {
                    multiTalkVO.setInputTokens(multiTalkVO.getInputTokens()+model.getInputTokens());
                    multiTalkVO.setOutputTokens(multiTalkVO.getOutputTokens()+model.getOutputTokens());
                }

                //若返回talkend灰化结束
                if(result.getOutput().getText().contains("TALKEND")){
                    //截取talkend前面的内容并加入list
                    String[] lastTalks = result.getOutput().getText().split("TALKEND") ;
                    if(lastTalks.length>0&&StringUtils.isNotBlank(lastTalks[0].trim())){
                        details.add(lastTalks[0].trim());
                    }
                    break;
                }

                details.add(result.getOutput().getText());

                //是否需要多轮会话，明确不要、或者输出不为1024倍数的
                if(!isMultiOutput||result.getUsage().getModels().get(0).getOutputTokens()%1024!=0){
                    break;
                }
                //继续输出的提示词
                param.setPrompt(String.format(PROMPT_ISEND,getLast20Words(result.getOutput().getText())));

            }

        }
        catch (Exception e) {
            logger.error("调用千问应用bot出错："+e.getMessage(),e);
            throw new RuntimeException(e);
        }

        StringBuilder stringBuilder=new StringBuilder(details.get(0));
        for (int i = 1; i < details.size(); i++) {
            //todo 这儿拼接可能会有问题，如A以"结尾，B以"开头，他原本是想要组成一个空字符串的，但这种拼接方式会只有一个"
            stringBuilder.append(JsonUtil.getAddString(details.get(i-1),details.get(i)));
        }

        multiTalkVO.setDetailTalks(details);
        multiTalkVO.setTotalContent(stringBuilder.toString());
        multiTalkVO.setSessionID(param.getSessionId());

        return multiTalkVO;
    }

    public static ResultVO baseChatWithModel(String systemContent, String question, String modelValue,
                                                 String assistant,String goOnContent){
        List<Message> messages=new ArrayList<>();
        Generation gen = new Generation();
        messages.add(Message.builder().role(Role.SYSTEM.getValue()).content(systemContent).build());
        messages.add(Message.builder().role(Role.USER.getValue()).content(question).build());
        if(StringUtils.isNotBlank(assistant)){
            messages.add(Message.builder().role(Role.ASSISTANT.getValue()).content(assistant).build());
            messages.add(Message.builder().role(Role.USER.getValue()).content(goOnContent).build());
        }
        GenerationParam param = GenerationParam.builder()
                // 若没有配置环境变量，请用百炼API Key将下行替换为：.apiKey("sk-xxx")
                .apiKey(APIKEY)
                .model(modelValue)
                .messages(messages)
                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                .build();
        GenerationResult result = null;
        try {
            //添加超时重试机制
            result=RetrySupplier.callAndRetryWhenTimeOut_Qwen(()->gen.call(param),
                    ()->gen.call(param),
                    logger,
                    TIMEOUT_RETRYCNT);
        }
        catch (Exception e) {
            logger.error("调用千问baseChatWithModel出错："+e.getMessage(),e);
            throw new RuntimeException(e);
        }
        logger.info("调用千问baseChatWithModel,token信息：{}，输出结果：{}",
                result.getUsage(),
                result.getOutput().getChoices().get(0).getMessage().getContent());

        return new ResultVO(result.getUsage().getInputTokens(), result.getUsage().getOutputTokens(),
            result.getOutput().getChoices().get(0).getMessage().getContent());
    }

    public static ResultVO baseChatWithModel_Img(String systemContent, String question, String modelValue,
                                                 String assistant,
                                                 List<String> imgs, String goOnContent){
        ResultVO resultVO = new ResultVO(0, 0, "");

        MultiModalConversation conv = new MultiModalConversation();

        List<MultiModalMessage> multiModalMessages = new ArrayList<>();
        //system信息
        MultiModalMessage systemMessage = MultiModalMessage.builder().role(Role.SYSTEM.getValue())
                .content(Arrays.asList(Collections.singletonMap("text", systemContent))).build();
        multiModalMessages.add(systemMessage);

        //图片信息
        List<Map<String, Object>> maps = new ArrayList<>();
        for (String img : imgs) {
            maps.add(Collections.singletonMap("image",img.replace("\\","/")));
        }
        maps.add(Collections.singletonMap("text",question));
        MultiModalMessage userMessage = MultiModalMessage.builder().role(Role.USER.getValue()).content(maps).build();
        multiModalMessages.add(userMessage);

        //多轮时，塞入ASSISTANT、USER
        if(StringUtils.isNotBlank(assistant)){
            MultiModalMessage assistantMessage= MultiModalMessage.builder().role(Role.ASSISTANT.getValue()).content(Arrays.asList(Collections.singletonMap("text", assistant))).build();
            MultiModalMessage goOnMessage=MultiModalMessage.builder().role(Role.USER.getValue()).content(Arrays.asList(Collections.singletonMap("text", goOnContent))).build();
            multiModalMessages.add(assistantMessage);
            multiModalMessages.add(goOnMessage);
        }

        MultiModalConversationParam param = MultiModalConversationParam.builder()
                // 若没有配置环境变量，请用百炼API Key将下行替换为：.apiKey("sk-xxx")
                .apiKey(APIKEY)
                .model(modelValue)
                .messages(multiModalMessages)
                //是否提高输入图片的默认Token上限。输入图片的默认Token上限为1280，配置为true时输入图片的Token上限为16384。此参数设置为false时增加图像理解速度
                .parameter("vl_high_resolution_images",false)
                .temperature(0f)
                .build();
        MultiModalConversationResult result = null;
        try {
            //添加超时重试机制
            result=RetrySupplier.callAndRetryWhenTimeOut_Qwen(()->conv.call(param),
                    ()->conv.call(param),
                    logger,
                    TIMEOUT_RETRYCNT);
        }
        catch (Exception e) {
            logger.error("调用千问baseChatWithModel_Img出错："+e.getMessage(),e);
            throw new RuntimeException(e);
        }
        logger.info("调用千问baseChatWithModel_Img,token信息：{}，输出结果：{}",
                result.getUsage(),
                result.getOutput().getChoices().get(0).getMessage().getContent().get(0).get("text"));
        resultVO.setData(result.getOutput().getChoices().get(0).getMessage().getContent().get(0).get("text").toString());
        resultVO.setInputTokens(result.getUsage().getInputTokens());
        resultVO.setOutputTokens(result.getUsage().getOutputTokens());
        return resultVO;
    }

    /**
     * 不允许直接调用baseChatWithBot，只允许使用QwenCaller来调用
     */
    public static class QwenCaller {
        private String question;
        private String botAppId;
        private String sessionId;
//        private List<String> imgs;
        private boolean isMultiOutput;

        //构造函数
        public QwenCaller(String botAppId){
            this.botAppId = botAppId;
        }

        public QwenCaller setQuestion(String question) {
            this.question = question;
            return this;
        }
//
//        public QwenCaller setBotAppId(String botAppId) {
//            this.botAppId = botAppId;
//            return this;
//        }

        public QwenCaller setSessionId(String sessionId) {
            this.sessionId = sessionId;
            return this;
        }

//        public QwenCaller setImgs(List<String> imgs) {
//            this.imgs = imgs;
//            return this;
//        }

        public QwenCaller setMultiOutput(boolean multiOutput) {
            isMultiOutput = multiOutput;
            return this;
        }

        //按照一般习惯提供下bulid方法，其实没作用
        public QwenCaller bulid(){
            return this;
        }

        public MultiTalkVO call(){
            if(StringUtils.isBlank(question)||StringUtils.isBlank(botAppId)){
                throw new RuntimeException("缺少对应的必要参数question或botAppId");
            }
            return baseChatWithBot(question,botAppId,sessionId,new ArrayList<>(),isMultiOutput);
        }
    }

    /**
     * 调用并记录日志
     *
     * @param logger1
     * @param logTitle
     * @param biConsumer
     * @param callStr
     * @return
     */
    public static <R,U>  U callAndLogtime(org.slf4j.Logger logger1,String logTitle,Function<R,U> biConsumer,
                                          R callStr ){
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        U apply =null;
        try {
            apply = biConsumer.apply(callStr);
        }
        finally {
            //获取sessionid
            stopWatch.stop();
            String result="";
            if(apply!=null&&apply.getClass().equals(MultiTalkVO.class)){
                MultiTalkVO multiTalkVO=(MultiTalkVO)apply;
                result="，sessionid："+multiTalkVO.getSessionID()+"，结果："+multiTalkVO.getTotalContent();
            }
            else if(apply!=null&&apply.getClass().equals(ResultVO.class)){
                ResultVO resultVO=(ResultVO)apply;
                result=",结果："+resultVO.getData();
            }
            logger1.info("{}，请求耗时{} ms {}",logTitle,stopWatch.getTime(),result);
        }

        return apply;
    }

    protected static String getLast20Words(String str){
        if (str.length() >= 20) {
            // 截取最后20个字符
            return str.substring(str.length() - 20);
        }
        else {
            return str;
        }
    }

//    protected static void callWithSession()
//            throws Exception {
//        String sessionid="";
//        ApplicationParam param = ApplicationParam.builder()
//                // 若没有配置环境变量，可用百炼API Key将下行替换为：.apiKey("sk-xxx")。但不建议在生产环境中直接将API Key硬编码到代码中，以减少API Key泄露风险。
//                .apiKey(APIKEY)
//                // 替换为实际的应用 ID
//                .appId("bc3d41bda71342d0a0b54cf01608fa4d")
//                .sessionId(sessionid)
//                .prompt("我正在测试文本最长长度，请返回一个500长度的任意中文" )
//                .build();
//
//        Application application = new Application();
//        ApplicationResult result = application.call(param);
//
//        param.setSessionId(result.getOutput().getSessionId());
//        System.out.println(result.getOutput().getSessionId());
////        param.setPrompt("你记得我刚问你哪里的天气吗?");
////        result = application.call(param);
//
//        System.out.printf("%s\n, session_id: %s\n",
//                result.getOutput().getText(), result.getOutput().getSessionId());
//    }
//
//    protected static void callWithStream() throws Exception {
//        Message user =
//                Message.builder().role(Role.USER.getValue()).content(
//                        "这儿输入内容").build();
//        GenerationParam param = GenerationParam.builder()
//                // 若没有配置环境变量，请用百炼API Key将下行替换为：.apiKey("sk-xxx")
//                .apiKey(APIKEY)
//                .model("qwen-plus")   // 此处以qwen-plus为例，您可按需更换模型名称。模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models
//                .messages(Arrays.asList(user))
//                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
//                .incrementalOutput(true)
//                .build();
//        Generation gen = new Generation();
//        Flowable<GenerationResult> result = gen.streamCall(param);
//        StringBuilder fullContent=new StringBuilder();
//        AtomicInteger i1=new AtomicInteger(0);
//        result.blockingForEach(message -> {
//            i1.set(message.getUsage().getOutputTokens());
//            fullContent.append(message.getOutput().getChoices().get(0).getMessage().getContent());
//        });
//        System.out.printf("%s\n, outputtokens: %s\n",
//                fullContent.toString(), i1);
//        System.exit(0);
//    }

}
