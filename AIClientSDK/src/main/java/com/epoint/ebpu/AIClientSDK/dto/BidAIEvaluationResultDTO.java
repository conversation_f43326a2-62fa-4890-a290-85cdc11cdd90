package com.epoint.ebpu.AIClientSDK.dto;

/**
 * AI评审结果表
 *
 */
public class BidAIEvaluationResultDTO {

	/**
	 * 唯一标识
	 */
	private String itemGuid;

	/**
	 * 评分点名称
	 */
	private String assessItemName;

	/**
	 * 评审标准
	 */
	private String itemCriteria;

	/**
	 * 招标要求
	 */
	private String bidRequirements;

	/**
	 * 投标响应
	 */
	private String tenderResponse;

	private String result;

	private String reason;
	
	private String position;

	public String getItemGuid() {
		return itemGuid;
	}

	public void setItemGuid(String itemGuid) {
		this.itemGuid = itemGuid;
	}

	public String getItemCriteria() {
		return itemCriteria;
	}

	public void setItemCriteria(String itemCriteria) {
		this.itemCriteria = itemCriteria;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getAssessItemName() {
		return assessItemName;
	}

	public void setAssessItemName(String assessItemName) {
		this.assessItemName = assessItemName;
	}

	public String getBidRequirements() {
		return bidRequirements;
	}

	public void setBidRequirements(String bidRequirements) {
		this.bidRequirements = bidRequirements;
	}

	public String getTenderResponse() {
		return tenderResponse;
	}

	public void setTenderResponse(String tenderResponse) {
		this.tenderResponse = tenderResponse;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}
}
