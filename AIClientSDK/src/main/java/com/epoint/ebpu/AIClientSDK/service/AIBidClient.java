package com.epoint.ebpu.AIClientSDK.service;

import java.io.InputStream;
import java.util.List;

import com.epoint.ebpu.AIClientSDK.client.enums.DomainType;
import com.epoint.ebpu.AIClientSDK.dto.*;

/**
 * 抽象类（约定对外接口）
 * <AUTHOR>
 *
 */
public abstract class AIBidClient {
	
	/**
	 * 评标办法提取
	 * @param zbpdf 招标文件pdf
	 * @return 评标办法里评审标准依旧是“符合投标人须知XXX项规定”
	 */
	public abstract BidAIAssessMethodPack pbbfStripper(InputStream zbpdf);
	
	/**
	 * 评审规则推理
	 * @param assessName 评审因素名称
	 * @param itemCriteria 评审标准
	 * @return
	 */
	public abstract BidAIAssessRuleReasonPack assessRuleReasoning(String assessName, String itemCriteria);
	
	/**
	 * 招标项目信息提取
	 * @param zbpdf 招标文件pdf
	 * @param requirement 招标要求子项集合
	 * @return
	 */
	public abstract BidAIRequirementPack bidFileDataStripper(InputStream zbpdf, List<BidAIRequirementDTO> requirement);

	/**
	 * 招标要求内容提取（评审表里招标要求部分提取反填）
	 * 评审规则表里评审标准是“符合投标人须知XXX项规定”的，或者招标要求留空的，此方法会进一步补充填充具体的要求内容
	 * @param zbpdf 招标文件pdf
	 * @param assessItemFactor 评审规则表招标要求部分
	 * @return
	 */
	public abstract BidAIAssessItemFactorPack requirementContentStripper(InputStream zbpdf, List<BidAIAssessItemFactorDTO> assessItemFactor);
	
	/**
	 * 投标响应内容提取（评审表里投标响应情况提取反填）
	 * @param assessItemFactor 评审规则表
	 * @param tenderFiles  投标响应材料
	 * @return
	 */
	public abstract BidAIAssessItemFactorPack responseContentStripper(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAITenderFileDTO> tenderFiles);
	
	/**
	 * AI智能评标
	 * @param assessItemFactor 评审规则表（招标要求和投标响应均已提取完成）
	 * @param requirement  招标要求内容（如果评审表招标要求完整，这个可传null）
	 * @param type 工程、采购等类型
	 * @return
	 */
	public abstract BidAIEvaluationResultPack evaluation(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAIRequirementDTO> requirement, DomainType type);
	
	
	/**
	 * AI智能评标-初步评审
	 * @param assessItemFactor 评审规则表
	 * @param requirement  招标要求内容
	 * @param tenderFiles  投标响应材料
	 * @param type 工程、采购等类型
	 * @return
	 */
	public abstract BidAIEvaluationResultPack evaluation(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAIRequirementDTO> requirement, List<BidAITenderFileDTO> tenderFiles, DomainType type);
	
	/**
	 * AI智能评标-详细打分
	 * @param assessItemFactor 评审规则表
	 * @param requirement  招标要求内容
	 * @param tenderFiles  投标响应材料
	 * @param type 工程、采购等类型
	 * @return
	 */
	public abstract BidAIEvaluationResultPack evaluationformark(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAIRequirementDTO> requirement, List<BidAITenderFileDTO> tenderFiles, DomainType type);

	/**
	 * 单位横向评审
	 * @param assessItemFactor 评审规则表
	 * @param requirement 招标要求内容
	 * @param type 工程、采购等类型
	 * @param preAIEvaluations 前置的打分信息，主要用的是投标响应内容
	 * @return
	 */
	public abstract BidAIHorizontalEvalPack tenderHorizontalEval(BidAIAssessItemFactorDTO assessItemFactor,List<BidAIRequirementDTO> requirement,DomainType type,List<BidAIHorizontalEvalDTO> preAIEvaluations);
}
