package com.epoint.ebpu.AIClientSDK.client.vo;

import java.util.List;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-03-18
 * @Version
 * @Description
 */
public class MultiTalkVO {
    private String totalContent;

    private List<String> detailTalks;

    private String sessionID;

    private long inputTokens;

    private long outputTokens;

    public String getTotalContent() {
        return totalContent;
    }

    public void setTotalContent(String totalContent) {
        this.totalContent = totalContent;
    }

    public List<String> getDetailTalks() {
        return detailTalks;
    }

    public void setDetailTalks(List<String> detailTalks) {
        this.detailTalks = detailTalks;
    }

    public String getSessionID() {
        return sessionID;
    }

    public void setSessionID(String sessionID) {
        this.sessionID = sessionID;
    }

    public long getInputTokens() {
        return inputTokens;
    }

    public void setInputTokens(long inputTokens) {
        this.inputTokens = inputTokens;
    }

    public long getOutputTokens() {
        return outputTokens;
    }

    public void setOutputTokens(long outputTokens) {
        this.outputTokens = outputTokens;
    }
}
