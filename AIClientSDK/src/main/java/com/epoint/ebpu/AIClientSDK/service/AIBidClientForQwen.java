package com.epoint.ebpu.AIClientSDK.service;

import com.epoint.ebpu.AIClientSDK.client.QwenAiUtils;
import com.epoint.ebpu.AIClientSDK.client.enums.DomainType;
import com.epoint.ebpu.AIClientSDK.client.prompts.QwenPrompts;
import com.epoint.ebpu.AIClientSDK.client.vo.MultiTalkVO;
import com.epoint.ebpu.AIClientSDK.client.vo.ResultVO;
import com.epoint.ebpu.AIClientSDK.dto.*;
import com.epoint.ebpu.AIClientSDK.util.AIStringUtil;
import com.epoint.ebpu.AIClientSDK.util.MarkdownUtil;
import com.epoint.ebpu.AIClientSDK.util.PDFConvertStringUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-03-17
 * @Version
 * @Description
 */
public class AIBidClientForQwen extends AIBidClient{
    protected static final org.slf4j.Logger logger = LoggerFactory.getLogger(AIBidClientForQwen.class);

    /**
     * ai评审，每次评审的评分点数量
     */
    protected static final int AIEvaluation_ItemsCnt=20;

    protected static final int MAXLENGTH_INPUT_ZB=170*1000;

    @Override
    public BidAIAssessMethodPack pbbfStripper(InputStream zbpdf) {
        logger.info("千问大模型sdk评标办法提取--开始");
        // pdf转文本
        String zbContent = truncateAiInputStr(PDFConvertStringUtil.parseByWordCom(zbpdf));
        // TODO 拆分下招标文件，剔除不在评审范围内的部分章节
        // 大模型提取评标办法
        MultiTalkVO multiTalkVO = QwenAiUtils.chatWithBot_PbbfStrip("请从该份招标文件提取评标办法：" + zbContent);
        String pbbfmd = multiTalkVO.getTotalContent();
        // md转对象，评标办法条款“评审因素”“评审标准”“评审项”“评审方式”“分值”
        List<Map<String, String>> pbbf = MarkdownUtil.md2List(pbbfmd);
        // 解析后按标准格式返回
        List<BidAIAssessMethodDTO> lst = new ArrayList<>();
        for (Map<String, String> r : pbbf) {
            BidAIAssessMethodDTO p = new BidAIAssessMethodDTO();
            p.setAssessItemName(r.get("评审因素"));
            p.setItemCriteria(r.get("评审标准"));
            p.setBlockTag(r.get("评审项"));
            p.setAssessType(r.get("评审方式"));
            p.setMark( AIStringUtil.dealEmptyStr(r.get("分值")));
            lst.add(p);
        }
        logger.info("千问大模型sdk评标办法提取--结束，" + lst.size() + "条");
        return new BidAIAssessMethodPack(multiTalkVO.getInputTokens(),multiTalkVO.getOutputTokens(),lst);
    }

    @Override
    public BidAIAssessRuleReasonPack assessRuleReasoning(String assessName, String itemCriteria) {
        logger.info("千问大模型sdk评审规则推理--开始");
        MultiTalkVO multiTalkVO = QwenAiUtils.chatWithBot_AssessRule(String.format("评审点：%s，评审标准：%s", assessName, itemCriteria));
        logger.info("千问大模型sdk评审规则推理--结束");
        return new BidAIAssessRuleReasonPack(multiTalkVO.getInputTokens(),multiTalkVO.getOutputTokens(),multiTalkVO.getTotalContent());
    }


    /**
     * 招标项目信息提取
     *
     * @param zbpdf
     *            招标文件pdf
     * @param requirement
     *            招标要求子项集合
     * @return
     */
    @Override
    public BidAIRequirementPack bidFileDataStripper(InputStream zbpdf, List<BidAIRequirementDTO> requirement) {
        logger.info("千问大模型sdk招标项目信息提取--开始");
        StringBuilder zbExtract = new StringBuilder();
        zbExtract.append("请提取出招标文件中");
        // 拼接输入的字段名
        for (BidAIRequirementDTO r : requirement) {
            zbExtract.append("“" + r.getRequireName() + "”");
        }
        zbExtract.append("的信息，");
        // 招标文件内容拼接
        zbExtract.append("招标文件内容如下：");
        String zbContent = truncateAiInputStr(PDFConvertStringUtil.parseByWordCom(zbpdf));
        zbExtract.append(zbContent);
        // 调用大模型提取
        MultiTalkVO zbresult = QwenAiUtils.chatWithBot_BidFileDataStripper(zbExtract.toString());
        // md转对象，“字段”“内容”
        List<Map<String, String>> requiremd = MarkdownUtil.md2List(zbresult.getTotalContent());
        // 解析后按标准格式返回
        List<BidAIRequirementDTO> lst = new ArrayList<>();
        for (Map<String, String> r : requiremd) {
            BidAIRequirementDTO p = new BidAIRequirementDTO();
            p.setRequireName(r.get("字段"));
            p.setRequireValue(r.get("内容"));
            lst.add(p);
        }
        logger.info("千问大模型sdk招标项目信息提取--结束，" + lst.size() + "条");
        return new BidAIRequirementPack(zbresult.getInputTokens(),zbresult.getOutputTokens(),lst);
    }

    @Override
    public BidAIAssessItemFactorPack requirementContentStripper(InputStream zbpdf, List<BidAIAssessItemFactorDTO> assessItemFactor) {
        if (assessItemFactor == null || assessItemFactor.isEmpty()) {
            return null;
        }
        logger.info("千问大模型sdk招标要求内容提取--开始");
        // 招标文件内容
        String zbContent = truncateAiInputStr(PDFConvertStringUtil.parseByWordCom(zbpdf));
        // 评审规则表（招标部分）
        StringBuilder rules = new StringBuilder();
        rules.append("请解析下招标要求信息，招标文件内容如下：").append("\n");
        rules.append(zbContent).append("\n");
        rules.append("评审规则表如下:").append("\n");
        rules.append("|唯一标识|序号|审查点|评审要求|招标文件规定|").append("\n");
        rules.append("| ---- | ---- | ---- | ---- | ---- |").append("\n");
        for (BidAIAssessItemFactorDTO pfd : assessItemFactor) {
            rules.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder()).append("|" + pfd.getAssessItemName())
                    .append("|" + AIStringUtil.dealEmptyStr(pfd.getItemCriteria())).append("|" + AIStringUtil.dealEmptyStr(pfd.getBidRequirements()) )
                    .append("|").append("\n");
        }
        MultiTalkVO multiTalkVO = QwenAiUtils.chatWithBot_RequirementStrip(rules.toString());
        String zbresult = multiTalkVO.getTotalContent();
        // md转对象
        List<Map<String, String>> requiremd = MarkdownUtil.md2List(zbresult);
        // 解析后按标准格式返回
        List<BidAIAssessItemFactorDTO> lst = new ArrayList<>();
        for (Map<String, String> r : requiremd) {
            BidAIAssessItemFactorDTO p = new BidAIAssessItemFactorDTO();
            p.setItemGuid(r.get("唯一标识"));
            p.setItemOrder(r.get("序号"));
            p.setAssessItemName(r.get("审查点"));
            p.setItemCriteria(AIStringUtil.dealEmptyStr(r.get("评审要求")));
            p.setBidRequirements(AIStringUtil.dealEmptyStr(r.get("招标文件规定")));
            lst.add(p);
        }
        logger.info("千问大模型sdk招标要求内容提取--结束，" + lst.size() + "条");
        return new BidAIAssessItemFactorPack(multiTalkVO.getInputTokens(),multiTalkVO.getOutputTokens(),lst);
    }

    @Override
    @Deprecated
    public BidAIAssessItemFactorPack responseContentStripper(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAITenderFileDTO> tenderFiles) {
        if (assessItemFactor == null || tenderFiles == null || assessItemFactor.isEmpty() || tenderFiles.isEmpty()) {
            return null;
        }
        logger.info("千问大模型sdk AI智能评标_投标响应情况提取-开始");

        // markdown整理出AI评审规则提示词
        StringBuilder psTable=new StringBuilder();
        StringBuilder psContent = new StringBuilder();
//        StringBuilder psRequirement = new StringBuilder();
        Map<String,String> otherArgs=new HashMap<>();

        psContent.append("响应情况提取规则表如下:{响应情况提取规则表}").append("\n");
        psTable.append("|唯一标识|序号|审查点|评审指标|投标响应情况|定位页码|").append("\n");
        psTable.append("| ---- | ---- | ---- | ---- | ---- | ---- |").append("\n");
        // 评标控制分批评分点和节点文件 防止输出过多
        for (BidAIAssessItemFactorDTO pfd : assessItemFactor) {
            psTable.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder())
                    .append("|" + pfd.getAssessItemName()).append("|" + pfd.getItemCriteria())
                    .append("|").append("|").append("|").append("\n");
        }

        // 提示词增加招标要求内容
//        if (requirement != null && !requirement.isEmpty()) {
//            psRequirement.append("已知招标文件中本项目的");
//            for (BidAIRequirementDTO r : requirement) {
//                psRequirement.append("“").append(r.getRequireName()).append("”是").append(r.getRequireValue()).append("，");
//            }
//            psRequirement.append("\n");
//        }

        otherArgs.put("响应情况提取规则表",psTable.toString());

        // 明确输出规范
        psContent.append("请开始提取，按markdown格式输出提取规则表。");
        // 图片路径作为输入
        List<String> imgUrls = new ArrayList<>();
        for (BidAITenderFileDTO ff : tenderFiles) {
            imgUrls.add(ff.getFilePath());
        }

        // 先进行提取技术响应情况
        ResultVO result = stripResponseWithModel_InvokeSDK(QwenPrompts.getTechResponsePrompt(),psContent.toString(),imgUrls,"",
                otherArgs);
        List<BidAIAssessItemFactorDTO> lst = new ArrayList<>();

        List<Map<String, String>> mapsStripNew = MarkdownUtil.md2List(result.getData());
        for (Map<String, String> r : mapsStripNew) {
            BidAIAssessItemFactorDTO p = new BidAIAssessItemFactorDTO();
            p.setItemGuid(r.get("唯一标识"));
            p.setAssessItemName(r.get("审查点"));
            p.setTenderResponse(r.get("投标响应情况"));
            p.setPosition(r.get("定位页码"));
            lst.add(p);
        }

        BidAIAssessItemFactorPack bidAIAssessItemFactorPack = new BidAIAssessItemFactorPack(result.getInputTokens(),
                result.getOutputTokens(),lst);

        logger.info("千问大模型sdk 投标响应情况提取--结束，" + bidAIAssessItemFactorPack.getData().size() + "条");
        return bidAIAssessItemFactorPack;
    }

    @Override
    @Deprecated
    public BidAIEvaluationResultPack evaluation(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAIRequirementDTO> requirement, DomainType type) {
        //拼接评审规则表
        List<Map<String, String>> mapsToEval=new ArrayList<>();
        for (BidAIAssessItemFactorDTO bidAIAssessItemFactorDTO : assessItemFactor) {
            Map<String, String> map=new LinkedHashMap<>();
            map.put("序号",bidAIAssessItemFactorDTO.getItemOrder());
            map.put("唯一标识",bidAIAssessItemFactorDTO.getItemGuid());
            map.put("审查点",bidAIAssessItemFactorDTO.getAssessItemName());
            map.put("评审要求",bidAIAssessItemFactorDTO.getItemCriteria());
            map.put("招标文件规定",bidAIAssessItemFactorDTO.getAssessRules());
            map.put("投标响应情况",bidAIAssessItemFactorDTO.getTenderResponse());
            map.put("AI评审规则",bidAIAssessItemFactorDTO.getAssessRules());
            map.put("AI评审理由","");
            map.put("AI评审结论","合格/不合格/提请人工判断");
            mapsToEval.add(map);
        }

        StringBuilder psContent = new StringBuilder();
        psContent.append("评审规则表如下:").append(MarkdownUtil.list2MD(mapsToEval)).append("\n");
        // 提示词增加招标要求内容
        if (requirement != null && !requirement.isEmpty()) {
            psContent.append("已知招标文件中本项目的");
            for (BidAIRequirementDTO r : requirement) {
                psContent.append("“" + r.getRequireName() + "”是" + r.getRequireValue() + "，");
            }
            psContent.append("\n");
        }
        // 明确输出规范
        psContent.append("请开始评审，按markdown格式输出评审表。");

        ResultVO resultVO = assessEvalFHXWithModel_InvokeSDK("", psContent.toString());

        // md转对象，“字段”“内容”
        List<Map<String, String>> resultmd = MarkdownUtil.md2List(resultVO.getData());
        // 解析后按标准格式返回
        List<BidAIEvaluationResultDTO> lst = new ArrayList<>();
        for (Map<String, String> r : resultmd) {
            BidAIEvaluationResultDTO p = new BidAIEvaluationResultDTO();
            p.setItemGuid(r.get("唯一标识"));
            p.setAssessItemName(r.get("审查点"));
            p.setItemCriteria(r.get("评审要求"));
            p.setBidRequirements(r.get("招标文件规定"));
            p.setTenderResponse(r.get("投标响应情况"));
            p.setReason(r.get("AI评审理由"));
            p.setResult(r.get("AI评审结论"));
            lst.add(p);
        }
        return new BidAIEvaluationResultPack(resultVO.getInputTokens(),resultVO.getOutputTokens(),lst);
    }

    /**
     * AI智能评标
     *
     * @param assessItemFactor
     *            评审规则表
     * @param requirement
     *            招标要求内容
     * @param tenderFiles
     *            投标响应材料
     * @return
     */
    @Override
    public BidAIEvaluationResultPack evaluation(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAIRequirementDTO> requirement, List<BidAITenderFileDTO> tenderFiles, DomainType type) {
        if (assessItemFactor == null || tenderFiles == null || assessItemFactor.isEmpty() || tenderFiles.isEmpty()) {
            return null;
        }
        logger.info("千问大模型sdk AI智能评标--开始");
        BidAIEvaluationResultPack bidAIEvaluationResultPack =evaluationWithModel(assessItemFactor, requirement, tenderFiles, type);
//        BidAIEvaluationResultPack bidAIEvaluationResultPack = evaluationWithApp(assessItemFactor, requirement, tenderFiles, type);
        logger.info("千问大模型sdk AI智能评标--结束，" + bidAIEvaluationResultPack.getData().size() + "条");
        return bidAIEvaluationResultPack;
    }

    @Override
    public BidAIEvaluationResultPack evaluationformark(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAIRequirementDTO> requirement, List<BidAITenderFileDTO> tenderFiles, DomainType type) {
        if (assessItemFactor == null || tenderFiles == null || assessItemFactor.isEmpty() || tenderFiles.isEmpty()) {
            return null;
        }
        logger.info("千问大模型sdk AI智能评标_打分--开始");

        // markdown整理出AI评审规则提示词
        StringBuilder psTable=new StringBuilder();
        StringBuilder psContent = new StringBuilder();
        StringBuilder psRequirement = new StringBuilder();
        Map<String,String> otherArgs=new HashMap<>();

        psContent.append("响应情况提取规则表如下:{响应情况提取规则表}").append("\n");
        psTable.append("|唯一标识|序号|审查点|评审指标|招标文件规定|投标响应情况|定位页码|").append("\n");
        psTable.append("| ---- | ---- | ---- | ---- | ---- | ---- | ---- |").append("\n");
        // 评标控制分批评分点和节点文件 防止输出过多
        for (BidAIAssessItemFactorDTO pfd : assessItemFactor) {
            psTable.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder())
                    .append("|" + pfd.getAssessItemName()).append("|" + pfd.getItemCriteria()).append("|" + pfd.getBidRequirements())
                    .append("| 响应度说明："+pfd.getTenderResponse()).append("，优点：{填充}，缺点：{填充}|").append("|").append("\n");
        }

        // 提示词增加招标要求内容
        if (requirement != null && !requirement.isEmpty()) {
            psRequirement.append("已知招标文件中本项目的");
            for (BidAIRequirementDTO r : requirement) {
                psRequirement.append("“").append(r.getRequireName()).append("”是").append(r.getRequireValue()).append("，");
            }
            psRequirement.append("\n");
        }

        otherArgs.put("响应情况提取规则表",psTable.append(psRequirement).toString());

        // 明确输出规范
        psContent.append("请开始提取，按markdown格式输出提取规则表。");
        // 图片路径作为输入
        List<String> imgUrls = new ArrayList<>();
        for (BidAITenderFileDTO ff : tenderFiles) {
            imgUrls.add(ff.getFilePath());
        }

        // 先进行提取技术响应情况
        ResultVO result = stripResponseWithModel_InvokeSDK(QwenPrompts.getTechResponsePrompt(),psContent.toString(),imgUrls,"",
                otherArgs);

        StringBuilder psContent_Assess = new StringBuilder();
        List<Map<String, String>> mapsToEval = MarkdownUtil.md2List(result.getData());
        mapsToEval.forEach(p->{
            Optional<BidAIAssessItemFactorDTO> assessItem = assessItemFactor.stream().filter(k -> k.getItemGuid().equals(p.get("唯一标识"))).findFirst();
            p.remove("定位页码");
            p.remove("投标响应情况_全文");
            p.put("招标文件规定",assessItem.orElseGet(BidAIAssessItemFactorDTO::new).getBidRequirements());
            p.put("AI评审规则",assessItem.orElseGet(BidAIAssessItemFactorDTO::new).getAssessRules());
            p.put("分数上限",assessItem.orElseGet(BidAIAssessItemFactorDTO::new).getMark());
            p.put("总得分","");
            p.put("得分理由","");
        });
        psContent_Assess.append("响应情况打分表内容:").append(MarkdownUtil.list2MD(mapsToEval)).append("\n ").append(psRequirement);

        ResultVO assessResult = assessTechMarkWithModel_InvokeSDK(QwenPrompts.getTechAssessResultPrompt(), psContent_Assess.toString());

        // md转对象，“字段”“内容”
        List<Map<String, String>> resultmd = MarkdownUtil.md2List(assessResult.getData());
        // 解析后按标准格式返回
        List<BidAIEvaluationResultDTO> lst = new ArrayList<>();
        //重新获取下对象
        List<Map<String, String>> mapsStripNew = MarkdownUtil.md2List(result.getData());
        for (Map<String, String> r : resultmd) {
            Optional<Map<String, String>> first = mapsStripNew.stream().filter(p -> r.get("唯一标识").equals(p.get(
                    "唯一标识"))).findFirst();
            BidAIEvaluationResultDTO p = new BidAIEvaluationResultDTO();
            p.setItemGuid(r.get("唯一标识"));
            p.setAssessItemName(r.get("审查点"));
            p.setTenderResponse(r.get("汇总响应情况"));
            p.setPosition(first.orElseGet(HashMap::new).get("定位页码"));
            p.setResult(r.get("总得分"));
            p.setReason(r.get("得分理由"));
            lst.add(p);
        }
        logger.info("千问大模型sdk AI智能评标_打分--结束，" + lst.size() + "条");
        return new BidAIEvaluationResultPack(result.getInputTokens()+assessResult.getInputTokens(),
                result.getOutputTokens()+assessResult.getOutputTokens(), lst);
    }

    @Override
    public BidAIHorizontalEvalPack tenderHorizontalEval(BidAIAssessItemFactorDTO assessItemFactor, List<BidAIRequirementDTO> requirement, DomainType type, List<BidAIHorizontalEvalDTO> preAIEvaluations) {
        if (assessItemFactor == null || preAIEvaluations == null || preAIEvaluations.isEmpty()) {
            return null;
        }
        logger.info("千问大模型sdk AI横向评审--开始");

        // 提示词增加招标要求内容
        StringBuilder psRequirement = new StringBuilder();
        if (requirement != null && !requirement.isEmpty()) {
            psRequirement.append("已知招标文件中本项目的");
            for (BidAIRequirementDTO r : requirement) {
                psRequirement.append("“").append(r.getRequireName()).append("”是").append(r.getRequireValue()).append("，");
            }
            psRequirement.append("\n");
        }

        StringBuilder psContent_Assess = new StringBuilder("单位横向评审表内容：");
        psContent_Assess.append("# 审查点内容及规则\n")
                .append("审查点：").append(assessItemFactor.getAssessItemName()).append("\n")
                .append("评审指标：").append(assessItemFactor.getItemCriteria()).append("\n")
                .append("AI评审规则：").append(assessItemFactor.getAssessRules()).append("\n")
                .append("招标文件规定：").append(assessItemFactor.getBidRequirements()).append("\n")
        ;
        List<Map<String, String>> mapsToEval=new ArrayList<>();
        for (BidAIHorizontalEvalDTO preAIEvaluation : preAIEvaluations) {
            Map<String, String> map = new LinkedHashMap<>();
            map.put("单位唯一标识", preAIEvaluation.getTenderGuid());
            map.put("投标响应情况", preAIEvaluation.getTenderResponse());
            map.put("分数上限", assessItemFactor.getMark());
            map.put("总得分", "");
            map.put("得分理由", "");
            mapsToEval.add(map);
        }
        psContent_Assess.append("\n# 评审表\b").append(MarkdownUtil.list2MD(mapsToEval));

        //拼接打分内容
        psContent_Assess.append(psRequirement).append("请开始提取，按markdown格式输出单位横向评审结果表。");

        MultiTalkVO multiTalkVO = QwenAiUtils.chatWithBot_HorizontalEval(psContent_Assess.toString());
        List<Map<String, String>> mapsPSResult = MarkdownUtil.md2List(multiTalkVO.getTotalContent());
        List<BidAIHorizontalEvalDTO> evalDTOS = mapsPSResult.stream().map(map -> {
            BidAIHorizontalEvalDTO evalDTO = new BidAIHorizontalEvalDTO();
            evalDTO.setTenderGuid(map.get("单位唯一标识"));
            evalDTO.setItemGuid(assessItemFactor.getItemGuid());
            evalDTO.setAssessItemName(assessItemFactor.getAssessItemName());
            evalDTO.setResult(map.get("总得分"));
            evalDTO.setReason(map.get("得分理由"));
            return evalDTO;
        }).collect(Collectors.toList());

        logger.info("千问大模型sdk AI横向评审--结束，" + evalDTOS.size() + "条");
        return new BidAIHorizontalEvalPack(multiTalkVO.getInputTokens(), multiTalkVO.getOutputTokens(), evalDTOS);
    }

    protected BidAIEvaluationResultPack evaluationWithApp(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAIRequirementDTO> requirement, List<BidAITenderFileDTO> tenderFiles, DomainType type){
        //通过List<BidAIAssessItemFactorDTO>获取拼接评审表，只是省略了单独定义方法
        Function<List<BidAIAssessItemFactorDTO>,StringBuilder> func_getPsContent=(list)->{
            // markdown整理出AI评审规则提示词
            StringBuilder psContent = new StringBuilder();
            psContent.append("评审规则表如下:").append("\n");
            psContent.append("|唯一标识|序号|审查点|评审要求|招标文件规定|投标响应情况|AI评审规则|AI评审理由|AI评审结论|").append("\n");
            psContent.append("| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |").append("\n");
            // 评标控制分批评分点和节点文件 防止输出过多
            for (BidAIAssessItemFactorDTO pfd : list) {
                psContent.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder())
                        .append("|" + pfd.getAssessItemName()).append("|" + pfd.getItemCriteria())
                        .append("|" + pfd.getBidRequirements()).append("|" + pfd.getTenderResponse())
                        .append("|" + pfd.getAssessRules()).append("|").append("|").append("合格/不合格/提请人工判断|").append("\n");
            }
            // 提示词增加招标要求内容
            if (requirement != null && !requirement.isEmpty()) {
                psContent.append("已知招标文件中本项目的");
                for (BidAIRequirementDTO r : requirement) {
                    psContent.append("“" + r.getRequireName() + "”是" + r.getRequireValue() + "，");
                }
                psContent.append("\n");
            }
            return psContent;
        };
        //通过List<Map<String, String>获取拼接结果表，只是省略了单独定义方法
        Function<List<Map<String, String>>,List<BidAIEvaluationResultDTO>> func_getResultDTO=(list)-> {
            // 解析后按标准格式返回
            List<BidAIEvaluationResultDTO> lst = new ArrayList<>();
            for (Map<String, String> r : list) {
                BidAIEvaluationResultDTO p = new BidAIEvaluationResultDTO();
                p.setItemGuid(r.get("唯一标识"));
                p.setAssessItemName(r.get("审查点"));
                p.setItemCriteria(r.get("评审要求"));
                p.setBidRequirements(r.get("招标文件规定"));
                p.setTenderResponse(r.get("投标响应情况"));
                p.setReason(r.get("AI评审理由"));
                p.setResult(r.get("AI评审结论"));
                lst.add(p);
            }
            return lst;
        };

        // pdf转图片base64
        List<String> imgUrls = new ArrayList<>();
        for (BidAITenderFileDTO ff : tenderFiles) {
//            imgUrls.add(ff.getFilePath());
            imgUrls.add(ff.getFileContent());
        }

        //第一轮提问：千问支持会话，第一次输入图片和前AIEvaluation_ItemsCnt个评分点
        String psContent = func_getPsContent.apply(assessItemFactor.stream().limit(AIEvaluation_ItemsCnt).collect(Collectors.toList()))
                .append(String.format("请开始评审，按markdown格式输出评审表。注意：我将分为%s轮评审，当前第%s轮，每轮的评审表内容不同，请注意甄别评审表不同。",
                        (assessItemFactor.size()-1) / AIEvaluation_ItemsCnt+1,1))
                .toString();
        MultiTalkVO multiTalkVO =QwenAiUtils.chatWithBot_AiEvaluation(psContent,imgUrls);
        // md转对象，“字段”“内容”
        List<Map<String, String>> resultmd = MarkdownUtil.md2List(multiTalkVO.getTotalContent());
        List<BidAIEvaluationResultDTO> result_First = func_getResultDTO.apply(resultmd);
        BidAIEvaluationResultPack ret=new BidAIEvaluationResultPack(multiTalkVO.getInputTokens(),
                multiTalkVO.getOutputTokens(),result_First);

        //其余的提问,注意的是后面轮使用会话形式，图片不用重复输入
        for (int i = 1; i < (assessItemFactor.size()-1) / AIEvaluation_ItemsCnt+1; i++){
            String psContent2 =
                    func_getPsContent.apply(assessItemFactor.stream().skip(i * AIEvaluation_ItemsCnt).limit(AIEvaluation_ItemsCnt).collect(Collectors.toList())).append(String.format("请开始评审，按markdown格式输出评审表。注意：我将分为%s轮评审，当前第%s轮，此轮的评审表内容和之前不同，请注意甄别评审表不同。",
                                    (assessItemFactor.size()-1) / AIEvaluation_ItemsCnt+1,i+1))
                            .toString();
            //使用caller塞入会话进行调用
            MultiTalkVO callResult = new QwenAiUtils.QwenCaller(QwenAiUtils.AIEVALUATION_BOTAPPID)
                    .setSessionId(multiTalkVO.getSessionID()).setMultiOutput(false).setQuestion(psContent2).call();
            List<Map<String, String>> resultmd2 = MarkdownUtil.md2List(callResult.getTotalContent());
            List<BidAIEvaluationResultDTO> result_Eval2 = func_getResultDTO.apply(resultmd2);
            //统计token，会话中大模型返回的是累加的，因此只要获取最后的就行了
            ret.setInputTokens(callResult.getInputTokens());
            ret.setOutputTokens(callResult.getOutputTokens());
            ret.getData().addAll(result_Eval2);
        }
        return ret;
    }

    protected BidAIEvaluationResultPack evaluationWithModel(List<BidAIAssessItemFactorDTO> assessItemFactor,
                                                        List<BidAIRequirementDTO> requirement, List<BidAITenderFileDTO> tenderFiles, DomainType type){
        // markdown整理出AI评审规则提示词
        StringBuilder psContent = new StringBuilder();
        StringBuilder psTable=new StringBuilder();
        Map<String,String> otherArgs=new HashMap<>();

        psContent.append("评审规则表如下:{评审规则表}").append("\n");
        // 提示词增加招标要求内容
        if (requirement != null && !requirement.isEmpty()) {
            psContent.append("已知招标文件中本项目的");
            for (BidAIRequirementDTO r : requirement) {
                psContent.append("“" + r.getRequireName() + "”是" + r.getRequireValue() + "，");
            }
            psContent.append("\n");
        }
        // 明确输出规范
        psContent.append("请开始评审，按markdown格式输出评审表。");

        //评审规则表
        psTable.append("|唯一标识|序号|审查点|评审要求|招标文件规定|投标响应情况|AI评审规则|AI评审理由|AI评审结论|").append("\n");
        psTable.append("| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |").append("\n");
        // 评标控制分批评分点和节点文件 防止输出过多
        for (BidAIAssessItemFactorDTO pfd : assessItemFactor) {
            psTable.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder())
                    .append("|" + pfd.getAssessItemName()).append("|" + pfd.getItemCriteria())
                    .append("|" + pfd.getBidRequirements()).append("|" + pfd.getTenderResponse())
                    .append("|" + AIStringUtil.dealEmptyStr2Model(pfd.getAssessRules())).append("|").append("|").append("合格/不合格/提请人工判断|").append(
                            "\n");
        }
        otherArgs.put("评审规则表",psTable.toString());

        //图片路径作为输入
        List<String> imgUrls = new ArrayList<>();
        for (BidAITenderFileDTO ff : tenderFiles) {
            imgUrls.add(ff.getFilePath());
        }

        // AI进行评审
        ResultVO resultVO = evaluationWithModel_InvokeSDK(QwenPrompts.getAIEvaluationPrompt_FHX(),psContent.toString(),imgUrls,
                "补充了部分投标文件材料，继续提取“投标响应情况”中缺失的关键信息后再次评审。需注意严格遵守system提示词生成结果，无法提取的内容仍保留表格中内容，不要臆想、猜测、推断。",otherArgs);
        // md转对象，“字段”“内容”
        List<Map<String, String>> resultmd = MarkdownUtil.md2List(resultVO.getData());
        // 解析后按标准格式返回
        List<BidAIEvaluationResultDTO> lst = new ArrayList<>();
        for (Map<String, String> r : resultmd) {
            BidAIEvaluationResultDTO p = new BidAIEvaluationResultDTO();
            p.setItemGuid(r.get("唯一标识"));
            p.setAssessItemName(r.get("审查点"));
            p.setItemCriteria(r.get("评审要求"));
            p.setBidRequirements(r.get("招标文件规定"));
            p.setTenderResponse(r.get("投标响应情况"));
            p.setReason(r.get("AI评审理由"));
            p.setResult(r.get("AI评审结论"));
            lst.add(p);
        }
        return new BidAIEvaluationResultPack(resultVO.getInputTokens(),resultVO.getOutputTokens(),lst);
    }

    /**
     * 直接使用模型进行ai评审
     *
     * @param systemContent
     * @param question
     * @param imgs
     * @param goOnContent
     * @param otherArgs
     * @return
     */
    protected ResultVO evaluationWithModel_InvokeSDK(String systemContent, String question, List<String> imgs,
                                                     String goOnContent, Map<String,String> otherArgs){
        if(imgs.isEmpty()){
            logger.error("图片数组信息为空");
            throw new RuntimeException("图片数组信息为空");
        }

        String modelValue=QwenAiUtils.MODEL_QWEN_25VL_32B;
        ResultVO result=new ResultVO(0,0,"");
        List<Map<String, String>> mapsToEval = MarkdownUtil.md2List(otherArgs.get("评审规则表"));
        List<Map<String, String>> mapsEvaled=new ArrayList<>();
        for (int i = 0; i < ((imgs.size() - 1) / QwenAiUtils.IMAGELIMITNUM_MODEL) + 1; i++) {
//            String nowRoundStr=String.format("\r\n\r\n #补充说明 \r\n" +
//                    "我投标文件材料将分为多轮输入，请按照真实情况对‘投标响应情况’重新检查是否已提供，若在本次提供了则需要重新提取，并对评审'AI" +
//                    "评审理由'和'AI" +
//                    "评审结论'重新评审(但是markdown表仍需完整返回)。当前轮%s,总轮数%s",i+1,((imgs.size() - 1) / IMAGELIMITNUM_MODEL) + 1);
//            final String assistant=StringUtils.isNotBlank(result.getData())?"上轮评审结果："+result.getData():"";
            List<String> inputImgs =imgs.stream().skip(i * QwenAiUtils.IMAGELIMITNUM_MODEL).limit(QwenAiUtils.IMAGELIMITNUM_MODEL).collect(Collectors.toList());

            String nowRoundQuestion=String.format("评审进度：当前第%s轮/共%s轮。",i+1,
                    ((imgs.size() - 1) / QwenAiUtils.IMAGELIMITNUM_MODEL) + 1)+ question.replace("{评审规则表}",
                    MarkdownUtil.list2MD(mapsToEval));
            ResultVO result_invoke = QwenAiUtils.callAndLogtime(logger, "AI智能评审第"+(i+1)+"轮",
                    r -> QwenAiUtils.baseChatWithModel_Img(systemContent,r,modelValue,"",inputImgs,
                            goOnContent),nowRoundQuestion);
            result.setOutputTokens(result.getOutputTokens()+result_invoke.getOutputTokens());
            result.setInputTokens(result.getInputTokens()+result_invoke.getInputTokens());

//            logger.info("ai评审，第{}轮输出{} \r\n",i+1,result);
            //最后一轮全塞
            if(i==((imgs.size() - 1) / QwenAiUtils.IMAGELIMITNUM_MODEL)){
                mapsEvaled.addAll(MarkdownUtil.md2List(result_invoke.getData()));
            }
            else {
                //将评审完的结果放到mapsEvaled
                List<Map<String, String>> tmpList = mapsToEval;
                mapsToEval = new ArrayList<>();
                List<Map<String, String>> nowRoundEvaled=MarkdownUtil.md2List(result_invoke.getData());
                for (Map<String, String> map : nowRoundEvaled) {
                    if ("合格".equals(map.get("AI评审结论"))) {
                        mapsEvaled.add(map);
                    }
                    else {
                        Optional<Map<String, String>> first = tmpList.stream().filter(p -> p.get("唯一标识").equals(map.get("唯一标识"))).findFirst();
                        if (first.isPresent()) {
                            mapsToEval.add(first.get());
                        }
                    }
                }
                //未返回的也要塞进去
                String guids = nowRoundEvaled.stream().map(p -> p.get("唯一标识")).collect(Collectors.joining(","));
                List<Map<String, String>> misEvaled = tmpList.stream().filter(p -> !guids.contains(p.get("唯一标识"))).collect(Collectors.toList());
                mapsToEval.addAll(misEvaled);
            }
            if(mapsToEval.isEmpty()){
                break;
            }
        }
        result.setData(MarkdownUtil.list2MD(mapsEvaled));
        return result;
    }

    /**
     * 使用模型提取响应情况
     *
     * @param systemContent
     * @param question
     * @param imgs
     * @param goOnContent
     * @param otherArgs
     * @return
     */
    protected ResultVO stripResponseWithModel_InvokeSDK(String systemContent, String question, List<String> imgs,
                                                        String goOnContent, Map<String,String> otherArgs){
        if(imgs.isEmpty()){
            logger.error("图片数组信息为空");
            throw new RuntimeException("图片数组信息为空");
        }

        String modelValue=QwenAiUtils.MODEL_QWEN_25VL_32B;
        ResultVO result=new ResultVO(0,0,"");
        List<Map<String, String>> mapsToStrip = MarkdownUtil.md2List(otherArgs.get("响应情况提取规则表"));
        List<Map<String, String>> mapsStriped=new ArrayList<>();
        //分为多轮获取响应情况
        for (int i = 0; i < ((imgs.size() - 1) / QwenAiUtils.IMAGELIMITNUM_MODEL) + 1; i++) {
//            String nowRoundStr=String.format("\r\n\r\n #补充说明 \r\n" +
//                    "我投标文件材料将分为多轮输入，请按照真实情况对‘投标响应情况’重新检查是否已提供，若在本次提供了则需要重新提取，并对评审'AI" +
//                    "评审理由'和'AI" +
//                    "评审结论'重新评审(但是markdown表仍需完整返回)。当前轮%s,总轮数%s",i+1,((imgs.size() - 1) / IMAGELIMITNUM_MODEL) + 1);
//            final String assistant=StringUtils.isNotBlank(result.getData())?"上轮评审结果："+result.getData():"";
            List<String> inputImgs =imgs.stream().skip(i * QwenAiUtils.IMAGELIMITNUM_MODEL).limit(QwenAiUtils.IMAGELIMITNUM_MODEL).collect(Collectors.toList());

            String nowRoundQuestion=String.format("提取进度：当前第%s轮/共%s轮。",i+1,
                    ((imgs.size() - 1) / QwenAiUtils.IMAGELIMITNUM_MODEL) + 1)+ question.replace("{响应情况提取规则表}",
                    MarkdownUtil.list2MD(mapsToStrip));
            ResultVO result_invoke = QwenAiUtils.callAndLogtime(logger, "AI评分响应情况提取，第"+(i+1)+"轮",
                    r -> QwenAiUtils.baseChatWithModel_Img(systemContent,r,modelValue,"",inputImgs,
                            goOnContent),nowRoundQuestion);
            result.setOutputTokens(result.getOutputTokens()+result_invoke.getOutputTokens());
            result.setInputTokens(result.getInputTokens()+result_invoke.getInputTokens());

//            logger.info("ai评审，第{}轮输出{} \r\n",i+1,result);
            //页码加上初始基数
            //第一轮直接全塞
            if(i==0){
                mapsStriped = MarkdownUtil.md2List(result_invoke.getData());
                for (Map<String, String> mapStriped : mapsStriped) {
                    StringBuilder stringBuilder=new StringBuilder();
                    StringBuilder sB_Cut=new StringBuilder();
                    String xyqk_Now = mapStriped.getOrDefault("投标响应情况", "");
                    mapStriped.put("投标响应情况_全文",stringBuilder.append("第").append(i+1).append("轮提取情况--").append(xyqk_Now).toString());
                    //定位页码、投标响应情况_截取处理
                    if(StringUtils.isNotBlank(mapStriped.getOrDefault("定位页码", ""))
                    &&!"无".equals(mapStriped.getOrDefault("定位页码", ""))){
                        mapStriped.put("定位页码",mapStriped.get("定位页码"));
                        //截取响应情况，用户不需要看到每一轮的优点，缺点
                        mapStriped.put("投标响应情况",
                                sB_Cut.append("第").append(i+1).append("轮提取情况--").append(getMiddleStr(xyqk_Now,
                                        "响应度说明","优点")).toString());
                    }
                    else {
                        mapStriped.put("定位页码",mapStriped.get(""));
                        mapStriped.put("投标响应情况","");
                    }
                }
            }
            else {
                //将提取完的信息加到mapsStriped
                List<Map<String, String>> mapsInvoke = MarkdownUtil.md2List(result_invoke.getData());
                for (Map<String, String> mapStriped : mapsStriped) {
                    Optional<Map<String, String>> first = mapsInvoke.stream().filter(p -> p.get("唯一标识").equals(mapStriped.get("唯一标识"))).findFirst();
                    if(first.isPresent()){
                        String xyqk_Now=first.get().get("投标响应情况");
                        StringBuilder stringBuilder = new StringBuilder(mapStriped.get("投标响应情况_全文"));
                        StringBuilder sB_Cut=new StringBuilder(mapStriped.getOrDefault("投标响应情况",""));
                        if(!StringUtils.endsWith(mapStriped.get("投标响应情况_全文"),"。")){
                            stringBuilder.append("。");
                            sB_Cut.append("。");//情况和投标响应情况一样的，就不拆分情况了
                        }
                        stringBuilder.append("第").append(i+1).append("轮提取情况--").append(xyqk_Now);

                        mapStriped.put("投标响应情况_全文", stringBuilder.toString());
                        //定位页面处理
                        String dwym_Now = findAndAddNum(first.get().getOrDefault("定位页码", ""), i * QwenAiUtils.IMAGELIMITNUM_MODEL, i * QwenAiUtils.IMAGELIMITNUM_MODEL);
                        // 页码为无，说明未提取到，直接跳过
                        if(!"无".equals(dwym_Now)){
                            mapStriped.put("定位页码",mapStriped.get("定位页码")+","+dwym_Now);
                            mapStriped.put("投标响应情况",sB_Cut.append("第").append(i+1).append("轮提取情况--").append(getMiddleStr(xyqk_Now,"响应度说明","优点")).toString());
                        }
                    }
                }
            }
        }
        result.setData(MarkdownUtil.list2MD(mapsStriped));
        return result;
    }

    /**
     * 调用大模型进行符合性评审
     *
     * @param systemContent
     * @param question
     * @return
     */

    @Deprecated
    protected ResultVO assessEvalFHXWithModel_InvokeSDK(String systemContent, String question){
        String modelValue=QwenAiUtils.MODEL_QWEN_25_32B;

        ResultVO resultVO = QwenAiUtils.callAndLogtime(logger, "AI符合性评审",
                r -> QwenAiUtils.baseChatWithModel(systemContent, r, modelValue, "", ""), question);
        return resultVO;
    }

    /**
     * 调用大模型进行打分评审
     *
     * @param systemContent
     * @param question
     * @return
     */
    protected ResultVO assessTechMarkWithModel_InvokeSDK(String systemContent, String question){
        String modelValue=QwenAiUtils.MODEL_QWEN_25_32B;

        ResultVO resultVO = QwenAiUtils.callAndLogtime(logger, "AI打分评审",
                r -> QwenAiUtils.baseChatWithModel(systemContent, r, modelValue, "", ""), question);
        return resultVO;
    }

    /**
     * 找到字符串中的数字，并加上一个数字
     *
     * @param str
     * @param addNumber
     * @param originNumber 初始基数编码
     * @return
     */
    public String findAndAddNum(String str,int addNumber,int originNumber){
        // 正则表达式：匹配整数
        String regex = "\\d+";

        // 创建 Pattern 对象
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);

        // 用来保存修改后的字符串
        StringBuffer result = new StringBuffer();

        // 查找所有数字并进行替换
        while (matcher.find()) {
            // 提取数字并转换为整数
            int number = Integer.parseInt(matcher.group());

            // 将数字增加指定的值
            number += addNumber;
            if(number<originNumber){
                number += originNumber;
            }

            // 将新的数字替换回原字符串
            matcher.appendReplacement(result, String.valueOf(number));
        }
        // 将剩余部分添加到结果中
        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 获取两个字符串中间的内容，并兼容markdown格式
     *
     * @param originS
     * @param preS
     * @param afterS
     * @return
     */
    public String getMiddleStr(String originS,String preS,String afterS){
        String regex = preS+"\\*?\\*?[：:]?(.+?)\\*?\\*?"+afterS;

        // 创建 Pattern 对象
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(originS);

        // 查找并输出匹配的部分
        if (matcher.find()) {
            // 获取第一个捕获组的内容（即"响应度说明"与"优点"之间的部分）
            return matcher.group(1);
        } else {
            return originS;
        }
    }

    protected String truncateAiInputStr(String inputStr){
        if(inputStr.length()>MAXLENGTH_INPUT_ZB){
            //超过长度进行截断
            logger.info("文件超长被截断，展示文件前文件前200字用于查找："+inputStr.substring(0,200));
            return inputStr.substring(0,MAXLENGTH_INPUT_ZB);
        }
        return inputStr;
    }
}
