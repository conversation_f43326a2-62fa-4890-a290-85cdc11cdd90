package com.epoint.ebpu.AIClientSDK.dto;

/**
 * AI评分点评审要素表
 *
 */
public class BidAIAssessItemFactorDTO
{

    /**
     * 唯一标识
     */
    private String itemGuid;

    /**
     * 评分点名称
     */
    private String assessItemName;

	/**
	 * 评审标准
	 */
	private String itemCriteria;
	
    /**
     * 评分点序号
     */
    private String itemOrder;

    /**
     * 评审类型
     */
    private String assessType;

    /**
     * 评审规则
     */
    private String assessRules;

    /**
     * 要求详解
     */
    private String requirementDetails;

    /**
     * 投标所需材料
     */
    private String bidRequiredMaterials;

    /**
     * 招标要求
     */
    private String bidRequirements;

    /**
     * 关联标书节点
     */
    private String relatedNodes;

    /**
     * 投标响应
     */
    private String tenderResponse;

    /**
     * 分值
     */
    private String mark;
    
    /**
     * 位置信息
     */
	private String position;

    /**
     * 是否横向
     */
    private boolean isHorizontal;

    public String getAssessItemName() {
        return assessItemName;
    }

    public void setAssessItemName(String assessItemName) {
        this.assessItemName = assessItemName;
    }

    public String getItemOrder() {
        return itemOrder;
    }

    public void setItemOrder(String itemOrder) {
        this.itemOrder = itemOrder;
    }

    public String getAssessType() {
        return assessType;
    }

    public void setAssessType(String assessType) {
        this.assessType = assessType;
    }

    public String getAssessRules() {
        return assessRules;
    }

    public void setAssessRules(String assessRules) {
        this.assessRules = assessRules;
    }

    public String getRequirementDetails() {
        return requirementDetails;
    }

    public void setRequirementDetails(String requirementDetails) {
        this.requirementDetails = requirementDetails;
    }

    public String getBidRequiredMaterials() {
        return bidRequiredMaterials;
    }

    public void setBidRequiredMaterials(String bidRequiredMaterials) {
        this.bidRequiredMaterials = bidRequiredMaterials;
    }

    public String getBidRequirements() {
        return bidRequirements;
    }

    public void setBidRequirements(String bidRequirements) {
        this.bidRequirements = bidRequirements;
    }

    public String getRelatedNodes() {
        return relatedNodes;
    }

    public void setRelatedNodes(String relatedNodes) {
        this.relatedNodes = relatedNodes;
    }

    public String getTenderResponse() {
        return tenderResponse;
    }

    public void setTenderResponse(String tenderResponse) {
        this.tenderResponse = tenderResponse;
    }

	public String getItemGuid() {
		return itemGuid;
	}

	public void setItemGuid(String itemGuid) {
		this.itemGuid = itemGuid;
	}

	public String getItemCriteria() {
		return itemCriteria;
	}

	public void setItemCriteria(String itemCriteria) {
		this.itemCriteria = itemCriteria;
	}

	public String getMark() {
		return mark;
	}

	public void setMark(String mark) {
		this.mark = mark;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

    public boolean isHorizontal() {
        return isHorizontal;
    }

    public void setHorizontal(boolean horizontal) {
        isHorizontal = horizontal;
    }
}
