package com.epoint.ebpu.AIClientSDK.client;

import com.epoint.ebpu.AIClientSDK.client.vo.ResultVO;
import com.epoint.ebpu.AIClientSDK.util.ImageConvertBase64;
import com.epoint.ebpu.AIClientSDK.util.MarkdownUtil;
import com.volcengine.ark.runtime.exception.ArkHttpException;
import com.volcengine.ark.runtime.model.Usage;
import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionRequest;
import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionResult;
import com.volcengine.ark.runtime.model.bot.completion.chat.usage.BotModelUsage;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionContentPart;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionResult;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang.StringUtils;

@Slf4j
public class VolcengineAiUtils {
	private static final String apiKey = System.getProperty("model_apiKey");
	private static final String model_text = System.getProperty("model_text");
	private static final String model_image = System.getProperty("model_image");
	private static final String botmodel_bid_gc = System.getProperty("botmodel_bid_gc");
	private static final String botmodel_bid_cg = System.getProperty("botmodel_bid_cg");
	private static final String botmodel_bid_tech = System.getProperty("botmodel_bid_tech");
	private static final String botmodel_pbbf = System.getProperty("botmodel_pbbf");
	private static final String botmodel_data = System.getProperty("botmodel_data");
	private static final String botmodel_zbyq = System.getProperty("botmodel_zbyq");
	private static final String botmodel_reason = System.getProperty("botmodel_reason");
	private static final String botmodel_tbxy = System.getProperty("botmodel_tbxy");
	private static final String botmodel_pf = System.getProperty("botmodel_pf");
	private static final String botmodel_sc = System.getProperty("botmodel_sc");
	private static final int IMAGELIMITNUM = 30;
	private static final int maxTokens = 12288;

	static ConnectionPool connectionPool = new ConnectionPool(10, 1, TimeUnit.SECONDS);
	static Dispatcher dispatcher = new Dispatcher();
	static ArkService service = null;

	public static void init(String baseUrl) {
		if (service != null) {

		} else {
			if (StringUtils.isNotBlank(baseUrl)) {
				service = ArkService.builder().dispatcher(dispatcher).connectionPool(connectionPool).baseUrl(baseUrl).apiKey(apiKey).build();
			}else{
				service = ArkService.builder().dispatcher(dispatcher).connectionPool(connectionPool).apiKey(apiKey).build();
			}
		}
	}

	/* 智能体部分 */
	/**
	 * 投标响应情况解析--智能体
	 * 
	 * @param question
	 * @return
	 */
	public static ResultVO chatForResponseAgent(String question, List<String> imgUrls) {
		if (service == null) {
			return new ResultVO(0, 0, "init error");
		}
		long t = System.currentTimeMillis();
		ResultVO result = new ResultVO(0, 0, "");
		List<Map<String, String>> resultLst = new ArrayList<>();
		int cnt = 0;
		int lc = 0;
		List<String> subimgUrls = new ArrayList<>();
		for (String url : imgUrls) {
			subimgUrls.add(url);
			cnt++;
			if (cnt == IMAGELIMITNUM) {// 达到图片限制数量，分多次问答
				ResultVO newresult = botChatCompletions(question, subimgUrls, botmodel_tbxy);
				result.setInputTokens(newresult.getInputTokens() + result.getInputTokens());
				result.setOutputTokens(newresult.getOutputTokens() + result.getOutputTokens());
				lc++;
				List<Map<String, String>> lst = MarkdownUtil.md2List(newresult.getData());
				if (!lst.isEmpty()) {
					for(Map<String, String> e : lst){
						e.put("唯一标识", e.get("唯一标识").replaceAll(" ", ""));
					}
					if (resultLst.isEmpty()) {
						resultLst = lst;
						for (Map<String, String> m : resultLst) {
							if ("未提取到关键信息".equals(m.get("投标响应情况")) || "无".equals(m.get("投标响应情况"))) {
								m.put("投标响应情况", "");
							}
							if ("无".equals(m.get("定位页码"))) {
								m.put("定位页码", "");
							}
						}
					} else {
						for (Map<String, String> m : resultLst) {
							Optional<Map<String, String>> fm = lst.stream()
									.filter(p -> m.get("唯一标识").equals(p.get("唯一标识"))).findFirst();
							if (fm.isPresent()) {
								if (!"未提取到关键信息".equals(fm.get().get("投标响应情况")) && !"无".equals(m.get("投标响应情况"))) {
									StringBuilder stringBuilder = new StringBuilder(m.get("投标响应情况"));
									if (StringUtils.isNotBlank(m.get("投标响应情况"))
											&& !StringUtils.endsWith(m.get("投标响应情况"), "。")) {
										stringBuilder.append("。");
									}
									stringBuilder.append("第").append(lc).append("轮提取情况--")
											.append(fm.get().get("投标响应情况"));
									m.put("投标响应情况", stringBuilder.toString());
								}
								if (!"无".equals(fm.get().get("定位页码"))) {
									m.put("定位页码", m.get("定位页码") + fm.get().get("定位页码"));
								}
							}
						}
					}
				}
				subimgUrls.clear();
				cnt = 0;
			}
		}
		if (!subimgUrls.isEmpty()) {
			ResultVO newresult = botChatCompletions(question, subimgUrls, botmodel_tbxy);
			result.setInputTokens(newresult.getInputTokens() + result.getInputTokens());
			result.setOutputTokens(newresult.getOutputTokens() + result.getOutputTokens());
			lc++;
			List<Map<String, String>> lst = MarkdownUtil.md2List(newresult.getData());
			if (!lst.isEmpty()) {
				for(Map<String, String> e : lst){
					e.put("唯一标识", e.get("唯一标识").replaceAll(" ", ""));
				}
				if (resultLst.isEmpty()) {
					resultLst = lst;
				} else {
					for (Map<String, String> m : resultLst) {
						Optional<Map<String, String>> fm = lst.stream().filter(p -> m.get("唯一标识").equals(p.get("唯一标识")))
								.findFirst();
						if (fm.isPresent()) {
							if (!"未提取到关键信息".equals(fm.get().get("投标响应情况")) && !"无".equals(m.get("投标响应情况"))) {
								StringBuilder stringBuilder = new StringBuilder(m.get("投标响应情况"));
								if (StringUtils.isNotBlank(m.get("投标响应情况"))
										&& !StringUtils.endsWith(m.get("投标响应情况"), "。")) {
									stringBuilder.append("。");
								}
								stringBuilder.append("第").append(lc).append("轮提取情况--").append(fm.get().get("投标响应情况"));
								m.put("投标响应情况", stringBuilder.toString());
							}
							if (!"无".equals(fm.get().get("定位页码"))) {
								m.put("定位页码", m.get("定位页码") + fm.get().get("定位页码"));
							}
						}
					}
				}
			}
		}
		result.setData(MarkdownUtil.list2MD(resultLst));
		log.info("chatForResponseAgent api耗时：" + (System.currentTimeMillis() - t));
		return result;
	}

	/**
	 * 评标打分--智能体
	 * 
	 * @param question
	 * @return
	 */
	public static ResultVO chatForBidAgent_mark(String question) {
		return botChatCompletions(question, botmodel_pf);
	}
	
	/**
	 * 评标符合性--智能体
	 * 
	 * @param question
	 * @return
	 */
	public static ResultVO chatForBidAgent_fhx(String question) {
		return botChatCompletions(question, botmodel_sc);
	}

	/**
	 * 招标要求评审规则推理--智能体
	 * 
	 * @param question
	 * @return
	 */
	public static ResultVO chatForReasonAgent(String question) {
		return botChatCompletions(question, botmodel_reason);
	}

	/**
	 * 招标文件项目信息提取--智能体
	 * 
	 * @param question
	 * @return
	 */
	public static ResultVO chatForDataAgent(String question) {
		return botChatCompletions(question, botmodel_data);
	}

	/**
	 * 招标文件评标办法解析--智能体
	 * 
	 * @param question
	 * @return
	 */
	public static ResultVO chatForPbbfAgent(String question) {
		return botChatCompletions(question, botmodel_pbbf);
	}

	/**
	 * 招标文件招标要求内容解析--智能体
	 * 
	 * @param question
	 * @return
	 */
	public static ResultVO chatForRequireAgent(String question) {
		return botChatCompletions(question, botmodel_zbyq);
	}

	/**
	 * 技术标评审智能体
	 * 
	 * @param question
	 * @param imgUrls
	 *            可以是下载地址，或图片Base64
	 * @param goOnContent
	 *            因图片数量限制再次触发的提示语
	 */
	public static ResultVO chatForBidAgent_tech(String question, List<String> imgUrls, String goOnContent) {
		return chatForBidAgent(question, imgUrls, goOnContent, botmodel_bid_tech);
	}

	/**
	 * 采购资格审查智能体
	 * 
	 * @param question
	 * @param imgUrls
	 *            可以是下载地址，或图片Base64
	 * @param goOnContent
	 *            因图片数量限制再次触发的提示语
	 */
	public static ResultVO chatForBidAgent_cg(String question, List<String> imgUrls, String goOnContent) {
		return chatForBidAgent(question, imgUrls, goOnContent, botmodel_bid_cg);
	}

	/**
	 * 建设工程资格审查智能体
	 * 
	 * @param question
	 * @param imgUrls
	 *            可以是下载地址，或图片Base64
	 * @param goOnContent
	 *            因图片数量限制再次触发的提示语
	 */
	public static ResultVO chatForBidAgent_gc(String question, List<String> imgUrls, String goOnContent) {
		return chatForBidAgent(question, imgUrls, goOnContent, botmodel_bid_gc);
	}

	/**
	 * 智能体调用-连续多轮
	 * 
	 * @param question
	 * @param imgUrls
	 *            可以是下载地址，或图片Base64
	 * @param goOnContent
	 *            因图片数量限制再次触发的提示语
	 */
	public static ResultVO chatForBidAgent(String question, List<String> imgUrls, String goOnContent, String botmodel) {
		if (service == null) {
			return new ResultVO(0, 0, "init error");
		}
		long t = System.currentTimeMillis();
		ResultVO result = new ResultVO(0, 0, "");
		int cnt = 0;
		List<String> subimgUrls = new ArrayList<>();
		for (String url : imgUrls) {
			subimgUrls.add(url);
			cnt++;
			if (cnt == IMAGELIMITNUM) {// 达到图片限制数量，分多次问答
				ResultVO newresult = botChatCompletionsWithAssistant(question, subimgUrls,
						StringUtils.isBlank(result.getData()) ? "" : "上轮评审结果：" + result.getData(), goOnContent,
						botmodel);
				result.setData(newresult.getData());
				result.setInputTokens(newresult.getInputTokens() + result.getInputTokens());
				result.setOutputTokens(newresult.getOutputTokens() + result.getOutputTokens());
				subimgUrls.clear();
				cnt = 0;
			}
		}
		if (!subimgUrls.isEmpty()) {
			ResultVO newresult = botChatCompletionsWithAssistant(question, subimgUrls,
					StringUtils.isBlank(result.getData()) ? "" : "上轮评审结果：" + result.getData(), goOnContent, botmodel);
			result.setData(newresult.getData());
			result.setInputTokens(newresult.getInputTokens() + result.getInputTokens());
			result.setOutputTokens(newresult.getOutputTokens() + result.getOutputTokens());
		}

		log.info("chatForBidAgent api耗时：" + (System.currentTimeMillis() - t));
		return result;
	}

	/* 基础调用方法 */

	/**
	 * 文本理解
	 * 
	 * @param sytemContent
	 * @param question
	 */
	public static ResultVO chatCompletions(String sytemContent, String question) {
		if (service == null) {
			return new ResultVO(0, 0, "init error");
		}
		long t = System.currentTimeMillis();
		final List<ChatMessage> messages = new ArrayList<>();
		if (StringUtils.isNotBlank(sytemContent)) {
			final ChatMessage systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(sytemContent)
					.build();
			messages.add(systemMessage);
		}
		final ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(question).build();
		messages.add(userMessage);

		ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder().maxTokens(maxTokens)
				.model(model_text).messages(messages).build();

		StringBuilder result = new StringBuilder();
		long in = 0;
		long out = 0;
		try {
			ChatCompletionResult chatCompletionResult = service.createChatCompletion(chatCompletionRequest);
			chatCompletionResult.getChoices().forEach(choice -> result.append(choice.getMessage().getContent()));
			try {
				Usage usage = chatCompletionResult.getUsage();
				in = usage.getPromptTokens();
				out = usage.getCompletionTokens();
			} catch (Exception x) {
			}
		} catch (ArkHttpException e) {
			log.error("大模型接口调用失败", e);
		}
		log.info("chatCompletions api耗时：" + (System.currentTimeMillis() - t) + "\n" + "输入tokens：" + in + "，输出tokens："
				+ out + "\n" + result.toString());
		return new ResultVO(in, out, result.toString());
	}

	/**
	 * 图文理解
	 * 
	 * @param sytemContent
	 * @param question
	 */
	public static ResultVO chatCompletions(String sytemContent, String question, List<String> imgUrls) {
		if (service == null) {
			return new ResultVO(0, 0, "init error");
		}
		long t = System.currentTimeMillis();
		final List<ChatMessage> messages = new ArrayList<>();
		if (StringUtils.isNotBlank(sytemContent)) {
			final ChatMessage systemMessage = ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(sytemContent)
					.build();
			messages.add(systemMessage);
		}
		List<ChatCompletionContentPart> multiParts = new ArrayList<>();
		multiParts.add(ChatCompletionContentPart.builder().type("text").text(question).build());
		for (String url : imgUrls) {
			multiParts.add(ChatCompletionContentPart.builder().type("image_url")
					.imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(url)).build());
		}
		ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiParts).build();
		messages.add(userMessage);

		ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder().maxTokens(maxTokens)
				.model(model_image).messages(messages).build();

		StringBuilder result = new StringBuilder();
		long in = 0;
		long out = 0;
		try {
			ChatCompletionResult chatCompletionResult = service.createChatCompletion(chatCompletionRequest);
			chatCompletionResult.getChoices().forEach(choice -> result.append(choice.getMessage().getContent()));
			try {
				Usage usage = chatCompletionResult.getUsage();
				in = usage.getPromptTokens();
				out = usage.getCompletionTokens();
			} catch (Exception x) {
			}
		} catch (ArkHttpException e) {
			log.error("大模型接口调用失败", e);
		}
		log.info("chatCompletions api耗时：" + (System.currentTimeMillis() - t) + "\n" + "输入tokens：" + in + "，输出tokens："
				+ out + "\n" + result.toString());
		return new ResultVO(in, out, result.toString());
	}

	/**
	 * 文本理解--应用--单轮
	 * 
	 * @param question
	 * 
	 */
	public static ResultVO botChatCompletions(String question, String model) {
		if (service == null) {
			return new ResultVO(0, 0, "init error");
		}
		long t = System.currentTimeMillis();
		List<ChatMessage> messages = new ArrayList<>();
		ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(question).build();
		messages.add(userMessage);

		BotChatCompletionRequest chatCompletionRequest = BotChatCompletionRequest.builder().maxTokens(maxTokens)
				.model(model).messages(messages).build();

		StringBuilder result = new StringBuilder();
		long in = 0;
		long out = 0;
		try {
			BotChatCompletionResult chatCompletionResult = service.createBotChatCompletion(chatCompletionRequest);
			chatCompletionResult.getChoices().forEach(choice -> result.append(choice.getMessage().getContent()));
			try {
				in = chatCompletionResult.getBotUsage().getModelUsage().stream()
						.mapToLong(BotModelUsage::getPromptTokens).sum();
				out = chatCompletionResult.getBotUsage().getModelUsage().stream()
						.mapToLong(BotModelUsage::getCompletionTokens).sum();
			} catch (Exception x) {
			}
		} catch (ArkHttpException e) {
			log.error("大模型接口调用失败", e);
		}
		log.info("botChatCompletions api耗时：" + (System.currentTimeMillis() - t) + "\n" + "输入tokens：" + in + "，输出tokens："
				+ out + "\n" + result.toString());
		return new ResultVO(in, out, result.toString());
	}

	/**
	 * 图文理解--应用--单轮
	 * 
	 * @param question
	 * @param imgUrls
	 *            可以是下载地址，或图片Base64，文件数量限制IMAGELIMITNUM以内
	 */
	public static ResultVO botChatCompletions(String question, List<String> imgUrls, String model) {
		if (service == null) {
			return new ResultVO(0, 0, "init error");
		}
		long t = System.currentTimeMillis();
		List<ChatMessage> messages = new ArrayList<>();
		List<ChatCompletionContentPart> multiParts = new ArrayList<>();
		multiParts.add(ChatCompletionContentPart.builder().type("text").text(question).build());
		for (String url : imgUrls) {
			multiParts.add(ChatCompletionContentPart.builder().type("image_url")
					.imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(url)).build());
		}
		ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiParts).build();
		messages.add(userMessage);

		BotChatCompletionRequest chatCompletionRequest = BotChatCompletionRequest.builder().maxTokens(maxTokens)
				.model(model).messages(messages).build();

		StringBuilder result = new StringBuilder();
		long in = 0;
		long out = 0;
		try {
			BotChatCompletionResult chatCompletionResult = service.createBotChatCompletion(chatCompletionRequest);
			chatCompletionResult.getChoices().forEach(choice -> result.append(choice.getMessage().getContent()));
			try {
				in = chatCompletionResult.getBotUsage().getModelUsage().stream()
						.mapToLong(BotModelUsage::getPromptTokens).sum();
				out = chatCompletionResult.getBotUsage().getModelUsage().stream()
						.mapToLong(BotModelUsage::getCompletionTokens).sum();
			} catch (Exception x) {
			}
		} catch (ArkHttpException e) {
			log.error("大模型接口调用失败", e);
		}
		log.info("botChatCompletions api耗时：" + (System.currentTimeMillis() - t) + "\n" + "输入tokens：" + in + "，输出tokens："
				+ out + "\n" + result.toString());
		return new ResultVO(in, out, result.toString());
	}

	/**
	 * 图文理解--应用--多轮
	 * 
	 * @param question
	 * @param imgUrls
	 *            可以是下载地址，或图片Base64，文件数量限制IMAGELIMITNUM以内
	 */
	public static ResultVO botChatCompletionsWithAssistant(String question, List<String> imgUrls, String assistant,
			String goOnContent, String model) {
		if (service == null) {
			return new ResultVO(0, 0, "init error");
		}
		long t = System.currentTimeMillis();
		List<ChatMessage> messages = new ArrayList<>();
		List<ChatCompletionContentPart> multiParts = new ArrayList<>();
		multiParts.add(ChatCompletionContentPart.builder().type("text").text(question).build());
		for (String url : imgUrls) {
			multiParts.add(ChatCompletionContentPart.builder().type("image_url")
					.imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(url)).build());
		}
		ChatMessage userMessage = ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiParts).build();
		messages.add(userMessage);
		if (StringUtils.isNotBlank(assistant)) {
			ChatMessage assistantMessage = ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(assistant)
					.build();
			messages.add(assistantMessage);
			ChatMessage goOnContentMessage = ChatMessage.builder().role(ChatMessageRole.USER).content(goOnContent)
					.build();
			messages.add(goOnContentMessage);
		}

		BotChatCompletionRequest chatCompletionRequest = BotChatCompletionRequest.builder().maxTokens(maxTokens)
				.model(model).messages(messages).build();

		StringBuilder result = new StringBuilder();
		long in = 0;
		long out = 0;
		try {
			BotChatCompletionResult chatCompletionResult = service.createBotChatCompletion(chatCompletionRequest);
			chatCompletionResult.getChoices().forEach(choice -> result.append(choice.getMessage().getContent()));
			try {
				in = chatCompletionResult.getBotUsage().getModelUsage().stream()
						.mapToLong(BotModelUsage::getPromptTokens).sum();
				out = chatCompletionResult.getBotUsage().getModelUsage().stream()
						.mapToLong(BotModelUsage::getCompletionTokens).sum();
			} catch (Exception x) {
			}
		} catch (ArkHttpException e) {
			log.error("大模型接口调用失败", e);
		}
		log.info("botChatCompletionsWithAssistant api耗时：" + (System.currentTimeMillis() - t) + "\n" + "输入tokens：" + in
				+ "，输出tokens：" + out + "\n" + result.toString());
		return new ResultVO(in, out, result.toString());
	}

	public static void close() {
		service.shutdownExecutor();
	}

	public static void main(String[] args) {
		init("");

		String tbpath = "C:\\Users\\<USER>\\Desktop\\资格审查资料图片\\";
		File[] imgs = new File(tbpath).listFiles();
		List<String> imgUrls = new ArrayList<>();
		for (File image : imgs) {
			imgUrls.add(ImageConvertBase64.toBase64(image, true));
		}
		ResultVO result = botChatCompletions(
				"分别对“营业执照”中的“企业名称”、“法定代表人”、“营业期限”和“经营范围”等四个要素逐项进行审查。（1）获取“营业执照”中的“企业名称”（设“企业名称”为N1）与【投标文件】的“投标函”中的企业名称（设“投标函”中的企业名称为N2）是否相同。相同的，即N2=N1的，为合格。否则，为不合格。（2）获取“营业执照”中的“法定代表人”（设“法定代表人”为R1）与【投标文件】的“投标函”中“法定代表人”的名称（设“投标函”中的“法定代表人”名称为R2）是否相同。相同的，即R2=R1的，为合格。否则，为不合格。（3）获取“营业执照”中的“营业期限”，设“营业期限”为L1。再获取【招标文件】中本项目开标时间（精确到年月日），设为L2，如果L1≥L2，即“营业执照”中的“营业期限”超过本项目开标时间的，该“营业期限”有效，否则为无效，评审为不合格。（4）获取“营业执照”中的“经营范围”信息，设经营范围为M1。获取【招标文件】中本项目的专业属性信息，设为M2，如果M2属于M1的子集，该经营范围有效，否则为无效，评审为不合格。以上四个要素检查全部合格的，营业执照评审结论为有效，否则为无效。评审完成后，生成以下形式的评审结论“初步评审-资格审查-营业执照有效性评审结论表”。"
						+ "初步评审-资格审查-营业执照有效性评审结论表（编号：ZGSC01）"
						+ "| 序号                                                         | 审查点                                                       | 招标文件规定             | 投标响应情况                                                 | AI评审说明                                                | AI评审结论               | 人工复核结论 |"
						+ "| ------------------------------------------------------------ | ------------------------------------------------------------ | ------------------------ | ------------------------------------------------------------ | --------------------------------------------------------- | ------------------------ | ------------ |"
						+ "| 1                                                            | 营业执照有效性                                               | 具备有效的营业执照       | “投标函”中的企业名称为【N2】，“营业执照”中的“企业名称”为【N1】 | “投标函”中的企业名称与“营业执照”中的“企业名称”一致/不一致 | 合格/不合格/提请人工判断 |              |"
						+ "| “投标函”中的“法定代表人”名称为【R2】，“营业执照”中的“法定代表人”为【R1】 | “投标函”中的“法定代表人”名称与“营业执照”中的“法定代表人”一致/不一致 | 合格/不合格/提请人工判断 |                                                              |                                                           |                          |              |"
						+ "| “营业执照”中的“营业期限”，为【L1】，【招标文件】中本项目开标时间为【L2】 | “营业执照”中的“营业期限”超出/未超出 【招标文件】中项目开标时间 | 合格/不合格/提请人工判断 |                                                              |                                                           |                          |              |"
						+ "| “营业执照”中的“经营范围”为【M1】，【招标文件】中本项目的专业属性信息为【M2】 | “营业执照”中的“经营范围”包括/不包括【招标文件】中项目的专业  | 合格/不合格/提请人工判断 |                                                              |                                                           |                          |              |",
				imgUrls, botmodel_bid_gc);
		log.info(result.getData());
		close();
	}
}
