package com.epoint.ebpu.AIClientSDK.service;

import com.epoint.ebpu.AIClientSDK.api.factory.AiPBFactory;
import com.epoint.ebpu.AIClientSDK.dto.BidAIAssessItemFactorDTO;
import com.epoint.ebpu.AIClientSDK.dto.epointagent.PostComplianceAssessDTO;
import com.epoint.ebpu.AIClientSDK.dto.epointagent.PostHorizontalAssessDTO;
import com.epoint.ebpu.AIClientSDK.util.AIStringUtil;
import com.epoint.ebpu.AIClientSDK.util.JsonUtil;
import com.epoint.ebpu.AIClientSDK.util.PostUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-05-15
 * @Version
 * @Description
 */
@Component
public class AIEpointAgentClient {
    /**
     * 符合性接口编排接口请求地址
     */
    public static final String COMPLIANCEASSESS_APIURI ="dynamicapi/complianceassess";

    /**
     * 技术标(打分)接口编排接口请求地址
     */
    public static final String MARKASSESS_APIURI ="dynamicapi/markassess";

    /**
     * 技术标(打分)横向评审接口编排接口请求地址
     */
    public static final String HORIZONTALASSESS_APIURI ="dynamicapi/horizontalassess";

    @Autowired
    protected AiPBFactory aiPBFactory;

    /**
     * 符合性评审智能体调用方法
     *
     * @param platformCode
     * @param bidsectionguid
     * @param bidsectionname
     * @param tenderGuids
     * @param bidAIAssessItemFactorDTOList
     * @return
     */
    public String complianceAssess(String requestId,String platformCode, String bidsectionguid, String bidsectionname,
                                   List<String> tenderGuids,List<BidAIAssessItemFactorDTO> bidAIAssessItemFactorDTOList){

        PostComplianceAssessDTO postComplianceAssessDTO = new PostComplianceAssessDTO();
        postComplianceAssessDTO.setRequestid(requestId);
        postComplianceAssessDTO.setPlatformcode(platformCode);
        postComplianceAssessDTO.setBidsectionguid(bidsectionguid);
        postComplianceAssessDTO.setBidsectionname(bidsectionname);
        postComplianceAssessDTO.setTenderguids(tenderGuids);
        postComplianceAssessDTO.setBidassessitemguids(bidAIAssessItemFactorDTOList.stream().map(BidAIAssessItemFactorDTO::getItemGuid).collect(Collectors.toList()));

        return PostUtil.httpPost(AIStringUtil.trimEnd(aiPBFactory.getPBToEpAgentService().agentUrl(platformCode),'/')+
                        "/rest/"+ COMPLIANCEASSESS_APIURI,
                JsonUtil.objectToJson(postComplianceAssessDTO));
    }

    /**
     * 技术（打分）评审智能体调用方法
     *
     * @param platformCode
     * @param bidsectionguid
     * @param bidsectionname
     * @param tenderGuids
     * @param bidAIAssessItemFactorDTOList
     * @return
     */
    public String markAssess(String requestId,String platformCode, String bidsectionguid, String bidsectionname,
                                   List<String> tenderGuids,List<BidAIAssessItemFactorDTO> bidAIAssessItemFactorDTOList){

        PostComplianceAssessDTO postComplianceAssessDTO = new PostComplianceAssessDTO();
        postComplianceAssessDTO.setRequestid(requestId);
        postComplianceAssessDTO.setPlatformcode(platformCode);
        postComplianceAssessDTO.setBidsectionguid(bidsectionguid);
        postComplianceAssessDTO.setBidsectionname(bidsectionname);
        postComplianceAssessDTO.setTenderguids(tenderGuids);
        postComplianceAssessDTO.setBidassessitemguids(bidAIAssessItemFactorDTOList.stream().map(BidAIAssessItemFactorDTO::getItemGuid).collect(Collectors.toList()));

        return PostUtil.httpPost(AIStringUtil.trimEnd(aiPBFactory.getPBToEpAgentService().agentUrl(platformCode),'/')+
                        "/rest/"+ MARKASSESS_APIURI,
                JsonUtil.objectToJson(postComplianceAssessDTO));
    }

    /**
     * 技术（打分）评审智能体调用方法
     *
     * @param requestId
     * @param platformCode
     * @param bidsectionguid
     * @param bidsectionname
     * @param bidAIAssessItemFactorDTOs
     * @return
     */
    public String horizontAlassess(String requestId,String platformCode, String bidsectionguid, String bidsectionname,
                             List<BidAIAssessItemFactorDTO> bidAIAssessItemFactorDTOs){

        PostHorizontalAssessDTO horizontalAssessDTO = new PostHorizontalAssessDTO();
        horizontalAssessDTO.setRequestid(requestId);
        horizontalAssessDTO.setPlatformcode(platformCode);
        horizontalAssessDTO.setBidsectionguid(bidsectionguid);
        horizontalAssessDTO.setBidsectionname(bidsectionname);
        horizontalAssessDTO.setBidassessitemguids(bidAIAssessItemFactorDTOs.stream().map(p->p.getItemGuid()).collect(Collectors.toList()));

        return PostUtil.httpPost(AIStringUtil.trimEnd(aiPBFactory.getPBToEpAgentService().agentUrl(platformCode),'/')+
                        "/rest/"+ HORIZONTALASSESS_APIURI,
                JsonUtil.objectToJson(horizontalAssessDTO));
    }
}
