package com.epoint.ebpu.AIClientSDK.controller.dto;

import java.util.List;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-25
 * @Version
 * @Description
 */
public class ZbfileparsingDTO {
    private List<Paragraphs> paragraphs;

    public List<Paragraphs> getParagraphs() {
        return paragraphs;
    }

    public void setParagraphs(List<Paragraphs> paragraphs) {
        this.paragraphs = paragraphs;
    }

    public static class Paragraphs {
        private String content;
        private int tokenslength;

        public Paragraphs(String content, int tokenslength) {
            this.content = content;
            this.tokenslength = tokenslength;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public int getTokenslength() {
            return tokenslength;
        }

        public void setTokenslength(int tokenslength) {
            this.tokenslength = tokenslength;
        }
    }
}
