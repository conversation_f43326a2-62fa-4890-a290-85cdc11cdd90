package com.epoint.ebpu.AIClientSDK.api.factory;

import com.epoint.ebpu.AIClientSDK.api.IPBToEpAgentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-25
 * @Version
 * @Description
 */
@Component
public class AiPBFactory {
    @Autowired
    protected ApplicationContext applicationContext;

    public IPBToEpAgentService getPBToEpAgentService(){
        return applicationContext.getBean(IPBToEpAgentService.class);
    }
}
