package com.epoint.ebpu.AIClientSDK.dto;

import java.util.List;

public class BidAIAssessMethodPack {
	private long inputTokens;
	private long outputTokens;
	private List<BidAIAssessMethodDTO> data;

	public BidAIAssessMethodPack(long in, long out, List<BidAIAssessMethodDTO> data) {
		this.inputTokens = in;
		this.outputTokens = out;
		this.data = data;
	}

	public long getInputTokens() {
		return inputTokens;
	}

	public void setInputTokens(long inputTokens) {
		this.inputTokens = inputTokens;
	}

	public long getOutputTokens() {
		return outputTokens;
	}

	public void setOutputTokens(long outputTokens) {
		this.outputTokens = outputTokens;
	}

	public List<BidAIAssessMethodDTO> getData() {
		return data;
	}

	public void setData(List<BidAIAssessMethodDTO> data) {
		this.data = data;
	}

}
