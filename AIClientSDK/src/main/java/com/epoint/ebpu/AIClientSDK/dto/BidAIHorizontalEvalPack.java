package com.epoint.ebpu.AIClientSDK.dto;

import java.util.List;

public class BidAIHorizontalEvalPack {
	private long inputTokens;
	private long outputTokens;
	private List<BidAIHorizontalEvalDTO> data;

	public BidAIHorizontalEvalPack(long in, long out, List<BidAIHorizontalEvalDTO> data) {
		this.inputTokens = in;
		this.outputTokens = out;
		this.data = data;
	}

	public long getInputTokens() {
		return inputTokens;
	}

	public void setInputTokens(long inputTokens) {
		this.inputTokens = inputTokens;
	}

	public long getOutputTokens() {
		return outputTokens;
	}

	public void setOutputTokens(long outputTokens) {
		this.outputTokens = outputTokens;
	}

	public List<BidAIHorizontalEvalDTO> getData() {
		return data;
	}

	public void setData(List<BidAIHorizontalEvalDTO> data) {
		this.data = data;
	}

}
