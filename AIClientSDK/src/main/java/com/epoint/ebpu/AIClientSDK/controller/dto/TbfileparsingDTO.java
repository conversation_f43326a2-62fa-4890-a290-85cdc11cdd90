package com.epoint.ebpu.AIClientSDK.controller.dto;

import java.util.List;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-25
 * @Version
 * @Description
 */
public class TbfileparsingDTO {
    private String tbfilepic_everytokens;
    private List<String> tbfilepic_localpath;
    private List<String> tbfilepic_base64;

    public String getTbfilepic_everytokens() {
        return tbfilepic_everytokens;
    }

    public void setTbfilepic_everytokens(String tbfilepic_everytokens) {
        this.tbfilepic_everytokens = tbfilepic_everytokens;
    }

    public List<String> getTbfilepic_localpath() {
        return tbfilepic_localpath;
    }

    public void setTbfilepic_localpath(List<String> tbfilepic_localpath) {
        this.tbfilepic_localpath = tbfilepic_localpath;
    }

    public List<String> getTbfilepic_base64() {
        return tbfilepic_base64;
    }

    public void setTbfilepic_base64(List<String> tbfilepic_base64) {
        this.tbfilepic_base64 = tbfilepic_base64;
    }
}
