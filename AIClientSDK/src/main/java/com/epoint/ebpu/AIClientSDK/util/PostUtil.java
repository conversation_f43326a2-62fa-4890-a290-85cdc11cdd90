package com.epoint.ebpu.AIClientSDK.util;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.time.Duration;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-05-15
 * @Version
 * @Description
 */
@Slf4j
public class PostUtil {
    public static String httpPost(String postUrl, String jsonContent) {

        OkHttpClient client = new OkHttpClient().newBuilder()
                .callTimeout(Duration.ofSeconds(86400))
                .connectTimeout(Duration.ofSeconds(86400))
                .readTimeout(Duration.ofSeconds(86400))
                .writeTimeout(Duration.ofSeconds(86400))
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, jsonContent);
        Request request = new Request.Builder()
                .url(postUrl)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "application/json")
                .build();
        Response response = null;
        try {
            response = client.newCall(request).execute();

            if (response.isSuccessful()) {
                return response.body().string();
            }
            else {
                throw new RuntimeException("调用出错，状态码：" + response.code() + "。详细信息：" + response.toString());
            }
        }
        catch (Exception e) {
            log.error("deepseek大模型调用出错", e);
            throw new RuntimeException(e);
        }
    }
}
