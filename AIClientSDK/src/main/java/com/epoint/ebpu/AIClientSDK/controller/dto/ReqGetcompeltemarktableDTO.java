package com.epoint.ebpu.AIClientSDK.controller.dto;

import java.util.List;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-05-16
 * @Version
 * @Description
 */
public class ReqGetcompeltemarktableDTO {
    private String accesstoken;
    private String platformcode;
    private String bidsectionguid;
    private String tenderguid;
    private List<String> bidassessitemguids;
    private List<String> roundsresult;

    public String getAccesstoken() {
        return accesstoken;
    }

    public void setAccesstoken(String accesstoken) {
        this.accesstoken = accesstoken;
    }

    public String getPlatformcode() {
        return platformcode;
    }

    public void setPlatformcode(String platformcode) {
        this.platformcode = platformcode;
    }

    public String getBidsectionguid() {
        return bidsectionguid;
    }

    public String getTenderguid() {
        return tenderguid;
    }

    public void setTenderguid(String tenderguid) {
        this.tenderguid = tenderguid;
    }

    public void setBidsectionguid(String bidsectionguid) {
        this.bidsectionguid = bidsectionguid;
    }

    public List<String> getBidassessitemguids() {
        return bidassessitemguids;
    }

    public void setBidassessitemguids(List<String> bidassessitemguids) {
        this.bidassessitemguids = bidassessitemguids;
    }

    public List<String> getRoundsresult() {
        return roundsresult;
    }

    public void setRoundsresult(List<String> roundsresult) {
        this.roundsresult = roundsresult;
    }
}
