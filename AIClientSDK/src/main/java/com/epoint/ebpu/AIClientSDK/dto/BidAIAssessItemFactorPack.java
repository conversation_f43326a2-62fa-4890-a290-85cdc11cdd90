package com.epoint.ebpu.AIClientSDK.dto;

import java.util.List;

public class BidAIAssessItemFactorPack {
	private long inputTokens;
	private long outputTokens;
	private List<BidAIAssessItemFactorDTO> data;
	
	public BidAIAssessItemFactorPack(long in, long out, List<BidAIAssessItemFactorDTO> data) {
		this.inputTokens = in;
		this.outputTokens = out;
		this.data = data;
	}

	public long getInputTokens() {
		return inputTokens;
	}

	public void setInputTokens(long inputTokens) {
		this.inputTokens = inputTokens;
	}

	public long getOutputTokens() {
		return outputTokens;
	}

	public void setOutputTokens(long outputTokens) {
		this.outputTokens = outputTokens;
	}

	public List<BidAIAssessItemFactorDTO> getData() {
		return data;
	}

	public void setData(List<BidAIAssessItemFactorDTO> data) {
		this.data = data;
	}

}
