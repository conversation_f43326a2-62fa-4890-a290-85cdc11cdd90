package com.epoint.ebpu.AIClientSDK.dto;

import java.util.List;

public class BidAIRequirementPack {
	private long inputTokens;
	private long outputTokens;
	private List<BidAIRequirementDTO> data;

	public BidAIRequirementPack(long in, long out, List<BidAIRequirementDTO> data) {
		this.inputTokens = in;
		this.outputTokens = out;
		this.data = data;
	}

	public long getInputTokens() {
		return inputTokens;
	}

	public void setInputTokens(long inputTokens) {
		this.inputTokens = inputTokens;
	}

	public long getOutputTokens() {
		return outputTokens;
	}

	public void setOutputTokens(long outputTokens) {
		this.outputTokens = outputTokens;
	}

	public List<BidAIRequirementDTO> getData() {
		return data;
	}

	public void setData(List<BidAIRequirementDTO> data) {
		this.data = data;
	}

}
