package com.epoint.ebpu.AIClientSDK.client.function;

import com.alibaba.dashscope.exception.ApiException;

/**
 * @Title 添加用于重试的函数
 * <AUTHOR>
 * @Date 2025-03-21
 * @Version
 * @Description
 */
@FunctionalInterface
public interface RetrySupplier<T> {
    T get() throws Exception;

    /**
     * 千问重试的方法，其他的话需要自己添加或完善此方法，因为添加了ApiException专门是千问的
     *
     * @param supplierFirst 第一次执行的方法
     * @param supplierRetry 出错后重试的方法
     * @param logger 日志对象
     * @param retryCnt 重试次数
     * @param <T>
     * @return
     */
    public static <T>  T callAndRetryWhenTimeOut_Qwen(RetrySupplier<T> supplierFirst, RetrySupplier<T> supplierRetry, org.slf4j.Logger logger, int retryCnt){
        int callCnt=(Math.max(retryCnt, 0))+1;
        for (int i = 0; i < callCnt; i++) {
            try {
                if(i==0) {
                    return supplierFirst.get();
                }
                else {
                    logger.info("重试，第"+(i+1)+"次调用开始");
                    return supplierRetry.get();
                }
            }
            catch (ApiException e){//仅处理ApiException
                if(i<callCnt-1&&(
                        (e.getMessage().contains("500")&&(e.getMessage().contains("timed out")||e.getMessage().contains("timeout")))
                                ||(e.getMessage().contains("400")&&e.getMessage().contains("Download multimodal file timed out")))){
                    //除了最后一次，前面的只是记录错误但不终止
                    logger.error("第"+(i+1)+"次调用调用api超时",e);
                }
                else {
                    //最后一次不记录，由下一层记录
                    throw new RuntimeException(e);
                }
            }
            catch (Exception ex){
                throw new RuntimeException(ex);
            }
        }
        return null;
    }
}
