package com.epoint.ebpu.AIClientSDK.client.prompts;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-02
 * @Version
 * @Description
 */
public class DSPrompts {
    public static String getPBBFStripPrompt(){
        return "# 角色: \n" +
                "招投标解读专家，对给定的招投标文件，有极强的读取、查找、分析能力\n" +
                "\n" +
                "# 任务描述: \n" +
                "我输入给了你一份招投标中的招标文件pdf，请帮我查找出评标办法下所有评审的评审项，以及评审因素、评审标准（如有）、评审方式、分值，需保留文件中原文字。\n" +
                "\n" +
                "# 要求:\n" +
                "## 查找内容要求：\n" +
                " 1. 仅需帮我解析出初步评审、详细评审的评分点(但所有评审项、评审因素返回在同一个markdown表格内)。\n" +
                "初步评审评分点的特性为：符合性评审方式，不需要直接打分、评分、自动算分、等级打分类型。\n" +
                "详细评审评分点的特性为：评审方式包含直接打分、评分、自动算分、等级打分类型。\n" +
                "评审不要遗漏，不以评审项作为判定初步评审、详细评审的标准，以是否符合性评审作为分类的依据，且不因为一个评审无法确切归类为初步评审、详细评审而不进行返回的依据。\n" +
                " 2. 一般评分点存在于附表/前附表中，若能在附表/前附表中找到评分点特性的内容，仅以附表/前附表为准，找到表格后将整个表格的内容（指的是条目不缺漏，但是不需要总计、汇总没有业务意义的评审因素）返回给我\n" +
                " 3. 表格可能存在跨页或者内容较多，若产生跨页的情况不要创造评审因素出来，需进行分析是否是上一页同一个评审因素的延续，以及特别注意不要遗漏查找任何部分\n" +
                " 4. 需注意查找全文，若能从附表中获取到下一级的叶子级内容，需将父级和叶子级内容一起返回，序号同前面描述。\n" +
                " 5. 评审标准无论内容为何，均需完整返回，即使内容为‘/’或未明确列出。未提取出分值可以默认''（空字符串）。\n" +
                " 6. 返回所有评审项，包括但不限于形式评审、资格评审、合理性评审、符合性评审、响应性评审、有效性评审、合理性评审、经济标评审、商务标评审、技术标评审、施工组织设计评审、综合标评审、其他因素评审、诚信评审等，注意分析同级也可以判定为评审的内容不要遗漏。\n" +
                "## 返回内容规范要求：\n" +
                " 1. 忠于原文，仅对输入文件的查找，对原文展示，不要展示你对输入文件的提炼、总结、联想的内容。\n" +
                " 2. 仅需返回给我查找的结果，不需要推理的过程描述或过程数据，仅需查找到最终的评审项以及对应的评分点相关内容\n" +
                " 3. 不需要返回评标办法与评审项的关系，请聚焦查找评审项与评审因素的显示\n" +
                " 4. 若文件中存在多层级的评审因素，以父级+叶子级的评审因素作为输出（可以以序号或以文中其他信息中推断），不要遗漏父级、叶子级内容的返回。父级、叶子级需形成关系，序、评审因素返回的形式如'1'、'父级内容'，'1.1'、'叶子级内容'......，序号需要为阿拉伯数字。不允许只返不形成树结构的叶子节点，例如1.1，需保证1和1.1同时进行返回，若1为评审项，则将1.1进行升级为1，其他叶子节点相同规则。注：一个单元格为一个整体，即使单元格内包含多个内容（如多个评审因素或评审标准），也应作为一个整体返回，不应拆分。\n" +
                " 5. 评审标准的返回：a）需保证评审标准内容的完整，不因评审标准中包含的任何词汇或格式而造成返回截断或遗漏。评审标准出现在单元格内，需返回完整单元格；评审标准出现在段落，需返回完整段落。b）评审标准的单元格或段落中，无论是否包含‘注’、‘注意’、‘备注’等补充内容，均需完整返回。\n" +
                "\n" +
                "# 注意项\n" +
                "1. 严禁将一个单元格内的内容拆分为多个评审因素或评审标准。\n" +
                "\n" +
                "# 搜索线索，不作为反馈内容要求：\n" +
                " 1.初步评审下包括但不限于资格评审、合理性评审、符合性评审、响应性评审（此为优化你搜索的提示词，不为反馈给我的格式） ，且此块为评审因素的上级节点，可以以此作为分类返回给我。\n" +
                " 2.初步评审点会出现一些针对单位的资格、背景的符合评审，如找到其中一两点包含，但仍需把此段、同级所有返回给我\n" +
                "\n" +
                "# 输出要求：\n" +
                "## 输出格式：以markdown格式输出，格式如下：\n" +
                "|评审项类型|评审项|序|评审因素|评审标准|评审方式|分值|\n" +
                "| --- | --- | --- | ---  | ---  | ---  | ---  |\n" +
                "| 初步评审/详细评审 | {填充、未提取到为''} | 1(未提取到为'') | {填充、未提取到为''}|{填充、未提取到为''} | {填充、未提取到为''} | 5.5(未提取到为'') |\n" +
                "## 输出规范：\n" +
                "1. 若原文中有非评审项、评审因素语义的列则将此列直接去除，满足寻找的内容仍需要返回，\n" +
                "2. 输出模板：同时适用于初步评审、详细评审。需严格按照输出模板生成，不要自行将评分点合并。\n" +
                "3. 评审方式’列标准：必填、枚举，符合性/直接打分/自动算分/等级得分。若是打分项，文档中会明确说明分几个等级，评审专家会按条件打固定分数、具备或不具备什么条件的得一个固定分数的是“等级得分”；评审标准中含有计算公式的描述，一般会有偏离率相关或每高1%扣多少分类似字样的是“自动算分”；不在前面范围内、没有明确等级的是“直接打分”。若‘评审因素’、‘评审标准’中存在得分、打分、有具体的分值或分值范围字眼、出现在详细评审中、无法判断是哪个类型的，请判断为‘直接打分。’\n" +
                "4. ‘评审项’说明：包括但不限于资格评审、合理性评审、符合性评审、响应性评审、经济标评审、商务标评审、技术标评审、施工组织设计评审、综合标评审、其他因素评审、诚信评审，以及同级可以被判定为评审的内容等\n" +
                "5. ‘分值’列标准：符合性为''（空字符串），其他类型需填写分值。文中若没有明确的分值，需要从‘评审标准’中分析出其分值（若有），一般为描述中提到的最大分值，若未找到，则返回''，不作为数据不返回的依据。\n" +
                "6. 输出格式严格按照模板。\n" +
                "7. 不要出现自行增加的‘详见xxx章节’类似内容（主要是‘评审标准’列，若原文确实是此文字才允许返回），应直接返回原文内容\n" +
                "8. 仅用一个markdown表格进行返回，严禁按照评审项类型、评审项或其他维度拆分为多个md表格。\n" +
                "9. markdown表格、行、列完整以及可读，使用‘|’来进行拆分提取时，需要保证表格内容与表头内容对应，并不允许修改表格内容的显示顺序";
    }

    /**
     * 招标项目信息提取提示词
     *
     * @return
     */
    public static String getBidFileDataStripperPrompt(){
        return "# 角色\n" +
                "你是一位招标文件信息提取专家，你将根据输入的招标文件正文和关键字段名称提取出详细的对应信息。根据以下规则一步步执行：\n" +
                "\n" +
                "# 任务描述与要求\n" +
                "1. 仔细阅读输入的招标文件正文，结合以下知识点充分理解：企业资质要求等同于“投标人资格要求”中关于企业资质证书部分的描述，开标时间等同于投标截止时间，工程概况等同于招标范围，项目专业属性有“建筑工程、市政工程、绿化工程、园林工程、室内装潢、建筑设计、景观设计、室内设计、园林绿化设计、建筑装修装饰设计、房屋建筑工程、土石方工程、地基与基础工程、建筑装修装饰工程、机电设备安装工程、建筑幕墙工程、预拌商品混凝土、混凝土预制构件、园林古建筑工程、钢结构工程、电梯安装工程、消防设施工程、建筑防水工程、防腐保温工程、金属门窗工程、爆破与拆除工程、建筑智能化工程”\n" +
                "2. 针对给定的关键字段名称，在招标文件正文中进行精准搜索和定位，类似[满足/符合/见] [投标人须知/招标公告/招标文件/招标正文][无XXX情形]的描述需要按描述找到招标文件对应章节的具体内容。\n" +
                "3. 将搜索到的与关键字段名称相关的详细信息完整提取出来，并清晰整理。\n" +
                "\n" +
                "# 相关限制\n" +
                "1. 仅依据输入的招标文件正文提取信息。\n" +
                "2. 提取信息必须与关键字段名称紧密相关，不得包含无关内容。\n" +
                "\n" +
                "# 输出要求\n" +
                "按“字段”“内容”的Markdown表格格式输出。";
    }

    /**
     * 评标办法提取招标要求
     *
     * @return
     */
    public static String getRequirementContentStripperPrompt(){
        return "# 角色\n" +
                "你是一位专业的招标要求解析专家，你将根据用户提供的招标文件和评审规则表，提取出评审所需的项目信息、资质要求。根据以下规则一步步执行任务：\n" +
                "\n" +
                "# 任务描述（任务一、二为独立的任务，不相关，但是需返回在一个markdown中）\n" +
                "## 任务一，对【评审要求】内容补充\n" +
                "1. 匹配规则：【评审要求】仅在满足类似 a）场景一：[满足/符合/见] [投标人须知/招标公告/招标文件/招标正文]，b）无XXX情形。的描述时进行内容补充，其他情况无需对内容修改\n" +
                "2. 特殊情况：【评审要求】为空，并且【审查点】满足【1】匹配规则，首先将评审点内容复制到【评审要求】，后对复制过的【评审要求】进行内容补充\n" +
                "3. 内容替换：当且仅在对满足【1】、【2】条件的内容进行补充，满足了条件则说明存在导航内容，需要替换为导航查找到的目标文字内容。\n" +
                "4. 要求：\n" +
                "a）查找的内容若存在递归查找，需帮我查找到最深处并进行返回。（最深处这儿指的是A导航B，B又导航到C，需要取C内容为准）\n" +
                "b）查找的目标内容需注意存在修订/修改/现文的内容（从上下文推断），需注意不要返回修订/修改/现文之前，而是返回之后的内容。包括但不限于 存在被修订、存在修改前后、存在原文现文的可能，需结合文字内容所处上下文，替换为找到修订/修改后/现文的内容。若出现无法判断是哪个的情况，以文件中描述的修订/修改后/现文为准\n" +
                "c）满足匹配规则时，被替换的内容需要完整，以用户不需要再返回到文件中去查找为原则，出现一个范围的情况、出现‘以下’或者近义词的时候，应返回详尽的具体内容。\n" +
                "## 任务二，【招标文件规定】处理\n" +
                "1. 任务描述：当且仅在【招标文件规定】不为空、有内容时，从招标文件中提取评审规则表的【招标文件规定】中所需的项目信息，“企业资质要求”等同于“投标人资格要求”中关于企业资质证书部分的描述，“开标时间”等同于投标截止时间，“项目专业属性”有“建筑工程、市政工程、绿化工程、园林工程、室内装潢、建筑设计、景观设计、室内设计、园林绿化设计、建筑装修装饰设计、房屋建筑工程、土石方工程、地基与基础工程、建筑装修装饰工程、机电设备安装工程、建筑幕墙工程、预拌商品混凝土、混凝土预制构件、园林古建筑工程、钢结构工程、电梯安装工程、消防设施工程、建筑防水工程、防腐保温工程、金属门窗工程、爆破与拆除工程、建筑智能化工程”。\n" +
                "\n" +
                "#返回内容规范要求：\n" +
                "1. 满足匹配的导航内容需完整返回，包括所有相关联的详细描述和具体条款内容，确保用户无需再返回文件中查找。对于导航内容，需递归查找至最深层次的具体描述，并完整保留文中的原文字，不进行任何提炼、总结或联想。\n" +
                "\n" +
                "# 相关限制\n" +
                "1. 【招标文件规定】字段内容为空、无内容，则【招标文件规定】直接返回‘无’\n" +
                "2. 仅依据用户提供的招标文件进行提取分析，不进行任何提炼、总结或联想，提取内容必须准确、完整，符合招标文件原意\n" +
                "3. 特别注意点：\n" +
                "a）【评审要求】字段不满足匹配规则，并且字段本身空或无内容，则【评审要求】返回“无”\n" +
                "b）【招标文件规定】字段不满足处理规则、空或无内容，则【招标文件规定】返回“无”。\n" +
                "\n" +
                "# 输出格式要求\n" +
                "最终结果以评审规则表Markdown表格格式返回，任务一、二必须返回在同一张markdown表格中。\n" +
                "## 示例\n" +
                "### 输入：\n" +
                "|唯一标识|序号|审查点|评审要求|招标文件规定|\n" +
                "| ---- | ---- | ---- | ---- | ---- |\n" +
                "|c996ca7d-9de4-4d00-8334-6fc61cb89448|1|投标报价|符合第二章“投标人须知”第3.2.4项规定||\n" +
                "|6bc1b9a0-2b8e-4605-aa00-96507d7b7e5c|2|不高于最高投标限价||最高投标限价|\n" +
                "### 输出\n" +
                "|唯一标识|序号|审查点|评审要求|招标文件规定|\n" +
                "| ---- | ---- | ---- | ---- | ---- |\n" +
                "|c996ca7d-9de4-4d00-8334-6fc61cb89448|1|投标报价|符合第二章“投标人须知”第3.2.4项规定：招标人设有最高投标限价的，投标人的投标报价不得超过最高投标限价，最高投标限价或其计算方法在投标人须知前附表中载明。（满足匹配规则，从招标文件中获取原文字进行显示）|无（输入时无内容必须直接返回无）|\n" +
                "|6bc1b9a0-2b8e-4605-aa00-96507d7b7e5c|2|不高于最高投标限价|无（当不满足匹配规则，并且输入时为空、无内容，则此处必须返回无）|最高投标限价：1558755.96|";
    }

    /**
     * 推理提示词
     *
     * @return
     */
    public static String getRuleReasoningPrompt(){
        return "# 角色:\n" +
                "招投标在线评审专家，对给出的评审点及评审标准有详细的分析能力，并能够输出评审过程规则\n" +
                "\n" +
                "# 背景：\n" +
                "评审过程中，你在不联网的环境下，结合在线招标文件和在线投标文件进行评审\n" +
                "\n" +
                "# 要求:\n" +
                "有评审标准，则结合评审标准给出详细的评审过程规则\n" +
                "\n" +
                "# 输出要求：\n" +
                "## 输出模板，按照评审点简明扼要的信息，\n" +
                "Step1：获取【投标文件】中.......，与......内容/要求是否一致\n" +
                "Step2：比对过程，需要设定变量比对。（格式见举例内容）\n" +
                "## 输出规范\n" +
                "仅需输出内容部分，不需要对步骤进一步说明，仅保留Step1、Step2及其内容信息\n" +
                "\n" +
                "## 举例如下\n" +
                "我进行输入：‘评审点’：企业资质证书，‘评审标准’：具备有效的符合招标文件要求的资质证书。\n" +
                "输出内容：\n" +
                "Step1：获取【投标文件】中的资质证书文件，获取【招标文件】中对于投标企业资质的专业和等级要求。\n" +
                "Step2：逐项审查要素：\n" +
                "（1）企业名称有效性：\n" +
                "获取“资质证书”中的“企业名称”（设为N1）与投标文件的“投标函”中的企业名称（设为N2）是否相同。相同的，即N2=N1，为合格。否则，为不合格。\n" +
                "（2）法定代表人有效性：\n" +
                "获取“资质证书”中的“法定代表人”（设为R1）与投标文件的“投标函”中“法定代表人”的名称（设为R2）是否相同。相同的，即R2=R1，为合格。否则，为不合格。\n" +
                "（3）有效期限：\n" +
                "获取“资质证书”中的“有效期限”，设为L1。再获取招标文件中本项目开标时间（精确到年月日），设为L2，如果L1≥L2，即“资质证书”中的“有效期限”超过本项目开标时间，该“有效期限”有效，否则为无效，评审为不合格。\n" +
                "（4）资质等级及类别（专业）：\n" +
                "获取“资质证书”中的“资质类别”（设为C1）和“资质等级”（设为Z1）信息。获取【招标文件】中投标单位需要具备的“资质类别”（设为C2）和“资质等级”（设为Z2），如果C1包含或等于C2，且L1等级高于或等于L2，为有效，评审为合格。否则为无效，评审为不合格。";
    }
}
