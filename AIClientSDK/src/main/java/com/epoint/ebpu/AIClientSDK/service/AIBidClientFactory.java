package com.epoint.ebpu.AIClientSDK.service;

import org.apache.commons.lang.StringUtils;

public class AIBidClientFactory {
	/**
	 * 初始化client
	 * @param type AIModelType枚举
	 * @return
	 */
	public static AIBidClient getClient(AIModelType type){
		return getClient(type, "");
	}
	
	/**
	 * 初始化client
	 * @param type AIModelType枚举
	 * @return
	 */
	public static AIBidClient getClient(AIModelType type, String agenturl){
		if(type == AIModelType.DOUBAO){
			AIBidClientForDoubao client = new AIBidClientForDoubao();
			client.init(agenturl);
			return client;
		}else if(type == AIModelType.QWEN){
			if (StringUtils.isNotBlank(agenturl)) {
				com.alibaba.dashscope.utils.Constants.baseHttpApiUrl = agenturl;
			}
			return new AIBidClientForQwen();
		}else if(type == AIModelType.KIMI){

			return null;
		}else if(type == AIModelType.DEEPSEEK){
			return new AIBidClient<PERSON>or<PERSON>eepSeek();
		}else{
			return null;
		}
	}
}
