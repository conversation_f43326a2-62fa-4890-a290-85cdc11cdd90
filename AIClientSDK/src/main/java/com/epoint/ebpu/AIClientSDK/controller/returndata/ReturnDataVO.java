package com.epoint.ebpu.AIClientSDK.controller.returndata;

import com.alibaba.fastjson.JSON;

import java.util.HashMap;

/**
 * @Author: XuJW
 * @Date: 2021-11-30 16:08
 * @Version: [1.0, 2021-11-30]
 * @copyright: 国泰新点软件有限公司
 * @Description:
 */
public class ReturnDataVO<T>
{
    private T custom;
    private StatusVO status;

    public static class StatusVO {
        private String text;
        private String code;

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }

    public ReturnDataVO() {
        this.status = new StatusVO();
    }

    public static String SUCCESS() {
        ReturnDataVO<HashMap> returnDataVo = new ReturnDataVO<>();
        returnDataVo.getStatus().setText("请求成功");
        returnDataVo.getStatus().setCode("1");
        returnDataVo.setCustom(new HashMap());
        return JSON.toJSON(returnDataVo).toString();
    }

    public static <T> String SUCCESS(T custom) {
        ReturnDataVO<T> returnDataVo = new ReturnDataVO<>();
        returnDataVo.setCustom(custom);
        returnDataVo.getStatus().setText("请求成功");
        returnDataVo.getStatus().setCode("1");
        return JSON.toJSON(returnDataVo).toString();
    }

    public static String Fail(String msg) {
        ReturnDataVO<HashMap> returnDataVo = new ReturnDataVO<>();
        returnDataVo.getStatus().setText(msg);
        returnDataVo.getStatus().setCode("0");
        returnDataVo.setCustom(new HashMap());
        return JSON.toJSON(returnDataVo).toString();

    }

    public T getCustom() {
        return custom;
    }

    public void setCustom(T custom) {
        this.custom = custom;
    }

    public StatusVO getStatus() {
        return status;
    }

    public void setStatus(StatusVO status) {
        this.status = status;
    }
}
