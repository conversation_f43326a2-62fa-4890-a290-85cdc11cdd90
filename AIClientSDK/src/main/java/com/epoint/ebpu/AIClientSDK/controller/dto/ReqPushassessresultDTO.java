package com.epoint.ebpu.AIClientSDK.controller.dto;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-25
 * @Version
 * @Description
 */
public class ReqPushassessresultDTO {
    private String accesstoken;
    private String platformcode;
    private String bidsectionguid;
    private String tenderguid;
    private String result;
    private String usetype;
    private String requestid_agent;

    public String getAccesstoken() {
        return accesstoken;
    }

    public void setAccesstoken(String accesstoken) {
        this.accesstoken = accesstoken;
    }

    public String getPlatformcode() {
        return platformcode;
    }

    public void setPlatformcode(String platformcode) {
        this.platformcode = platformcode;
    }

    public String getBidsectionguid() {
        return bidsectionguid;
    }

    public void setBidsectionguid(String bidsectionguid) {
        this.bidsectionguid = bidsectionguid;
    }

    public String getTenderguid() {
		return tenderguid;
	}

	public void setTenderguid(String tenderguid) {
		this.tenderguid = tenderguid;
	}

	public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getUsetype() {
        return usetype;
    }

    public void setUsetype(String usetype) {
        this.usetype = usetype;
    }

    public String getRequestid_agent() {
        return requestid_agent;
    }

    public void setRequestid_agent(String requestid_agent) {
        this.requestid_agent = requestid_agent;
    }
}
