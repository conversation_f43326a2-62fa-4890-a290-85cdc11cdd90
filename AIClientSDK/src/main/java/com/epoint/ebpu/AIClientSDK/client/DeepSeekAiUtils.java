package com.epoint.ebpu.AIClientSDK.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.epoint.ebpu.AIClientSDK.client.enums.MessageType;
import com.epoint.ebpu.AIClientSDK.client.vo.ChatMessage;
import com.epoint.ebpu.AIClientSDK.client.vo.ResultVO;
import com.epoint.ebpu.AIClientSDK.client.vo.deepseek.ResponseDataVO;
import okhttp3.*;
import org.slf4j.LoggerFactory;

import java.time.Duration;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-02
 * @Version
 * @Description
 */
public class DeepSeekAiUtils {
    protected static final org.slf4j.Logger logger = LoggerFactory.getLogger(DeepSeekAiUtils.class);

    private static final String APIKEY = System.getProperty("model_apiKey");

    public static final String CHAT_MODEL="deepseek-chat";
    public static final String REASONER_MODEL="deepseek-reasoner";

    /**
     * 对话模型（V3模型）
     *
     * @param messages
     * @return
     */
    public static ResultVO chatCompletions_Chat(ChatMessage... messages){
        return chatCompletions(CHAT_MODEL,messages);
    }

    /**
     * 推理模型（R1模型）
     *
     * @param messages
     * @return
     */
    public static ResultVO chatCompletions_Reasoner(ChatMessage... messages){
        return chatCompletions(REASONER_MODEL,messages);
    }

    public static ResultVO chatCompletions(String model,ChatMessage... messages){
        JSONObject requestBody = new JSONObject()
                .fluentPut("messages", JSONArray.parseArray(JSON.toJSON(messages).toString()))
                .fluentPut("model",model)
                .fluentPut("frequency_penalty",0)
                .fluentPut("max_tokens",8192)
                .fluentPut("presence_penalty",0)
                .fluentPut("response_format",new JSONObject().put("type","text"))
                .fluentPut("stop",null)
                .fluentPut("stream",false)
                .fluentPut("stream_options",null)
                .fluentPut("temperature",0.8)//这儿改成了0.8
                .fluentPut("top_p",1)
                .fluentPut("tools",null)
                .fluentPut("tool_choice","none")
                .fluentPut("logprobs",false)
                .fluentPut("top_logprobs",null);

        OkHttpClient client = new OkHttpClient().newBuilder()
                .callTimeout(Duration.ofSeconds(600))
                .connectTimeout(Duration.ofSeconds(600))
                .readTimeout(Duration.ofSeconds(300))
                .writeTimeout(Duration.ofSeconds(60))
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, requestBody.toString());
        Request request = new Request.Builder()
                .url("https://api.deepseek.com/chat/completions")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept", "application/json")
                .addHeader("Authorization", "Bearer "+APIKEY)
                .build();
        Response response=null;
        try {
            response = client.newCall(request).execute();

            if(response.isSuccessful()) {
                String responseStr = response.body().string();
                ResponseDataVO responseDataVO = JSONObject.parseObject(responseStr, ResponseDataVO.class);
                return new ResultVO(responseDataVO.getUsage().getPrompt_tokens(),
                        responseDataVO.getUsage().getCompletion_tokens(),
                        responseDataVO.getChoices().get(0).getMessage().getContent());
            }
            else {
                if(response.code()==402){
                    throw new RuntimeException("状态码：402，账户未开通或存在欠费情况");
                }
                else {
                    throw new RuntimeException("调用出错，状态码：" + response.code() + "。详细信息：" + response.toString());
                }
            }
        }
        catch (Exception e) {
            logger.error("deepseek大模型调用出错",e);
            throw new RuntimeException(e);
        }
    }

//    public static void main(String[] args) {
//        String s = QwenAiUtils.callAndLogtime(logger,"main测试",r->
//                chatCompletions(new ChatMessage(MessageType.system,
//                        "你是一个智能问答助手"),
//                new ChatMessage(MessageType.user,"今天天气怎么样"),
//                new ChatMessage(MessageType.assistant,
//                        "你可以通过查看天气预报应用或网站获取实时天气信息。如果你已经授权位置权限，我也可以帮你查询。请告诉我你的城市或允许定位，我会尽力提供准确的天气情况！ ☀️\uD83C\uDF27️⛅"),
////                new ChatMessage(MessageType.user,"张家港天气怎么样"),
////                new ChatMessage(MessageType.assistant,
////                        "目前我无法实时获取天气数据，但你可以通过以下方式快速查询张家港的天气：\n\n1. 打开手机天气APP（如墨迹天气、彩云天气等），允许定位或手动输入\"张家港\"\n2. 在搜索引擎输入\"张家港天气\"，通常会显示实时数据和未来24小时预报\n3. 支付宝/微信搜索\"天气服务\"小程序\n\n如需详细帮助，可以告诉我你想了解：\n- 实时温度\n- 降水概率\n- 空气质量\n- 未来几天预报\n我会指导你如何快速查到这些信息！ 🌦️\n\n（温馨提示：张家港近期多阵雨，出门建议带伞哦~）"),
//                new ChatMessage(MessageType.user,"明天好像要下暴雨，需要带多大的雨伞合适")
//        ),"") .getData();
//        System.out.println(s);
//    }
}
