package com.epoint.ebpu.AIClientSDK.api;

import com.epoint.ebpu.AIClientSDK.api.enums.BidPFDType;
import com.epoint.ebpu.AIClientSDK.dto.*;

import java.io.InputStream;
import java.util.List;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-25
 * @Version
 * @Description
 */
public interface IPBToEpAgentService {
    /**
     * token校验
     *
     * @param token
     * @return
     */
    boolean validateToken(String platformcode,String token);

    /**
     * 新点agent地址，如http://**************:8080/EpointFrame
     *
     * @return
     */
    String agentUrl(String platformcode);

    /**
     * 获取招标文件文件流
     * 【Z1】使用
     *
     * @param platformcode
     * @param bidsectionguid
     * @return
     */
    InputStream getZBFileInputStream(String platformcode,String bidsectionguid);

    /**
     * 获取AI评分点评审要素表（用于提取招标要求）
     * 【Z2】使用
     *
     * @param platformcode
     * @param bidsectionguid
     * @param pfdtype 默认：空全部，1：符合性评审，2：打分
     * @return
     */
    List<BidAIAssessItemFactorDTO> getAssessItemFactor4Require(String platformcode, String bidsectionguid, BidPFDType pfdtype);

    /**
     * 接收招标要求提取结果
     * 【Z3】使用
     *
     * @param requestid_agent
     * @param platformcode
     * @param bidsectionguid
     * @param assessItemFactorDTOS
     */
    void pushzbrequirementresult(String requestid_agent,String platformcode,String bidsectionguid,
                                 List<BidAIAssessItemFactorDTO> assessItemFactorDTOS);

    /**
     * 提供投标文件信息，
     * 需按照提供的评分点，对应的标书pdf切图
     * 注意：若是图纸，需要提高切图的像素，提高识别
     * 【T1】使用
     *
     * @param platformcode
     * @param bidsectionguid
     * @param tenderguid
     * @param bidassessitemguid
     * @param isNeedBase64
     * @return
     */
    List<BidAITenderFileDTO> getTenderFiles(String platformcode,String bidsectionguid,String tenderguid,
                                            List<String> bidassessitemguid,boolean isNeedBase64);

    /**
     * 提供待评审的评审表
     * 【H1,H9】使用
     *
     * @param platformcode
     * @param bidsectionguid
     * @param bidassessitemguids
     * @return
     */
    List<BidAIAssessItemFactorDTO> getCommonItemFactor4Assess(String platformcode, String bidsectionguid, List<String> bidassessitemguids);

    /**
     * 提供待评审的单位评审表
     * （这个场景下投标响应情况需要加上外部的提取结果，如三方网站证照查询结果）
     * 【T2,T3,J1】使用
     *
     * @param platformcode
     * @param bidsectionguid
     * @param tenderguid
     * @param bidassessitemguids
     * @return
     */
    List<BidAIAssessItemFactorDTO> getTenderItemFactor4Assess(String platformcode, String bidsectionguid,String tenderguid,List<String> bidassessitemguids);

    /**
     * 提供招标信息（通用）
     * 【T2、J1、H1】多使用
     *
     * @param platformcode
     * @param bidsectionguid
     * @return
     */
    List<BidAIRequirementDTO> getZBRequirement(String platformcode, String bidsectionguid);

    /**
     * 推送评审结果
     * 【T3】使用
     *
     * @param requestid_agent
     * @param platformcode
     * @param bidsectionguid
     * @param tenderguid
     * @param bidAIEvaluationResultDTOS
     */
    void pushassessresult(String requestid_agent,String platformcode,String bidsectionguid,String tenderguid,
                          List<BidAIEvaluationResultDTO> bidAIEvaluationResultDTOS);

    /**
     * 获取单一评分点的横向评审单位
     * 【H1】使用
     *
     * @param platformcode
     * @param bidsectionguid
     * @param assessItemFactorDTO
     * @return
     */
    List<BidAIHorizontalEvalDTO> getAIIHorizontalEvalTenders(String platformcode,String bidsectionguid,
                                                             BidAIAssessItemFactorDTO assessItemFactorDTO);

    /**
     * 横向评审结果推送接口
     * 【H9】单评分点维度横向评审结果推送
     *
     * @param requestid_agent
     * @param platformcode
     * @param bidsectionguid
     * @param bidassessitemguid
     * @param bidAIHorizontalEvalResult
     */
    void pushhorizontalresult(String requestid_agent,String platformcode,String bidsectionguid,String bidassessitemguid,
                              List<BidAIHorizontalEvalDTO> bidAIHorizontalEvalResult);
}
