package com.epoint.ebpu.AIClientSDK.util;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MarkdownUtil {

	public static List<Map<String, String>> md2List(String content) {
		List<Map<String, String>> tableData = new ArrayList<>();
		List<String> headers = new ArrayList<>();
		Map<String, String> rowData = null;
		int cols = 0;
		int index = 0;
		String regex = "\\|(.*?)\\|";
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(content);
		boolean sp = false;
		boolean hasReadContent=false;
		int start = 0;
		while (matcher.find(start)) {
			String txt = matcher.group(1);
			start = matcher.end() - 1;
			if (txt.contains("\n")) {
				continue;
			}
			txt = txt.trim();
			if (txt.startsWith("-") && txt.endsWith("-")&&!hasReadContent) {
				cols++;
				sp = true;
				continue;
			}
			txt = txt.replaceAll("<br/>", "");
			if (!sp) {
				headers.add(txt);
			} else {
				hasReadContent=true;
				if (rowData == null) {
					rowData = new LinkedHashMap<>();
				}
				rowData.put(headers.get(index % cols), txt);
				if ((index + 1) % cols == 0) {
					tableData.add(rowData);
					rowData = null;
				}
				index++;
			}
		}
		return tableData;
	}

	public static String list2MD(List<Map<String, String>> list){
		if(list.isEmpty()){
			return "";
		}

		StringBuilder ret=new StringBuilder();
		//拼接头部
		for (String s : list.get(0).keySet()) {
			ret.append("|").append(s);
		}
		ret.append("|\n");
		for (int i = 0; i < list.get(0).size(); i++) {
			ret.append("| ---- ");
		}
		ret.append("|\n");
		//拼接内容
		for (Map<String, String> map : list) {
			//以第0条的作为筛选条件
			for (String s : list.get(0).keySet()) {
				ret.append("|").append(map.get(s));
			}
			ret.append("|\n");
		}
		return ret.toString();
	}
}
