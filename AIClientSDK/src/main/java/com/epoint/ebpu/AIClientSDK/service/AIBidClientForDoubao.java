package com.epoint.ebpu.AIClientSDK.service;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.epoint.ebpu.AIClientSDK.client.VolcengineAiUtils;
import com.epoint.ebpu.AIClientSDK.client.enums.DomainType;
import com.epoint.ebpu.AIClientSDK.client.vo.ResultVO;
import com.epoint.ebpu.AIClientSDK.dto.*;
import com.epoint.ebpu.AIClientSDK.util.MarkdownUtil;
import com.epoint.ebpu.AIClientSDK.util.PDFConvertStringUtil;
import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class AIBidClientForDoubao extends AIBidClient {

	// 初始化
	public void init(String baseUrl) {
		log.info("初始化doubao大模型sdk");
		VolcengineAiUtils.init(baseUrl);
	}

	// 关闭
	public void close() {
		log.info("关闭doubao大模型sdk");
		VolcengineAiUtils.close();
	}

	/**
	 * 评标办法提取
	 * 
	 * @param zbpdf
	 *            招标文件pdf
	 * @return
	 */
	@Override
	public BidAIAssessMethodPack pbbfStripper(InputStream zbpdf) {
		log.info("doubao大模型sdk评标办法提取--开始");
		// pdf转文本
		String zbContent = PDFConvertStringUtil.parseByWordCom(zbpdf);
		// TODO 拆分下招标文件，剔除不在评审范围内的部分章节
		// 大模型提取评标办法
		ResultVO pbbfmd = VolcengineAiUtils.chatForPbbfAgent("请从该份招标文件提取评标办法：" + zbContent);
		// md转对象，评标办法条款“评审因素”“评审标准”“评审项”“评审方式”“分值”
		List<Map<String, String>> pbbf = MarkdownUtil.md2List(pbbfmd.getData());
		// 解析后按标准格式返回
		List<BidAIAssessMethodDTO> lst = new ArrayList<>();
		for (Map<String, String> r : pbbf) {
			BidAIAssessMethodDTO p = new BidAIAssessMethodDTO();
			p.setAssessItemName(r.get("评审因素"));
			p.setItemCriteria(r.get("评审标准"));
			p.setBlockTag(r.get("评审项"));
			p.setAssessType(r.get("评审方式"));
			p.setMark(r.get("分值"));
			lst.add(p);
		}
		log.info("doubao大模型sdk评标办法提取--结束，" + lst.size() + "条");
		return new BidAIAssessMethodPack(pbbfmd.getInputTokens(), pbbfmd.getOutputTokens(), lst);
	}

	/**
	 * 评审规则推理
	 * 
	 * @param assessName
	 *            评审因素名称
	 * @param itemCriteria
	 *            评审标准
	 * @return
	 */
	@Override
	public BidAIAssessRuleReasonPack assessRuleReasoning(String assessName, String itemCriteria) {
		log.info("doubao大模型sdk评审规则推理--开始");
		String question = "评审点：" + assessName + "\n评审标准：" + itemCriteria;
		ResultVO result = VolcengineAiUtils.chatForReasonAgent(question);
		log.info("doubao大模型sdk评审规则推理--结束");
		return new BidAIAssessRuleReasonPack(result.getInputTokens(), result.getOutputTokens(), result.getData());
	}

	/**
	 * 招标项目信息提取
	 * 
	 * @param zbpdf
	 *            招标文件pdf
	 * @param requirement
	 *            招标要求子项集合
	 * @return
	 */
	@Override
	public BidAIRequirementPack bidFileDataStripper(InputStream zbpdf, List<BidAIRequirementDTO> requirement) {
		log.info("doubao大模型sdk招标项目信息提取--开始");
		StringBuilder zbExtract = new StringBuilder();
		zbExtract.append("请提取出招标文件中");
		// 拼接输入的字段名
		for (BidAIRequirementDTO r : requirement) {
			zbExtract.append("“" + r.getRequireName() + "”");
		}
		zbExtract.append("的信息，");
		// 招标文件内容拼接
		zbExtract.append("招标文件内容如下：");
		String zbContent = PDFConvertStringUtil.parseByWordCom(zbpdf);
		zbExtract.append(zbContent);
		// 调用大模型提取
		ResultVO zbresult = VolcengineAiUtils.chatForDataAgent(zbExtract.toString());
		// md转对象，“字段”“内容”
		List<Map<String, String>> requiremd = MarkdownUtil.md2List(zbresult.getData());
		// 解析后按标准格式返回
		List<BidAIRequirementDTO> lst = new ArrayList<>();
		for (Map<String, String> r : requiremd) {
			BidAIRequirementDTO p = new BidAIRequirementDTO();
			p.setRequireName(r.get("字段"));
			p.setRequireValue(r.get("内容"));
			lst.add(p);
		}
		log.info("doubao大模型sdk招标项目信息提取--结束，" + lst.size() + "条");
		return new BidAIRequirementPack(zbresult.getInputTokens(), zbresult.getOutputTokens(), lst);
	}

	/**
	 * 招标要求内容提取
	 * 
	 * @param zbpdf
	 *            招标文件pdf
	 * @param assessItemFactor
	 *            评审规则表招标要求部分
	 * @return
	 */
	@Override
	public BidAIAssessItemFactorPack requirementContentStripper(InputStream zbpdf,
			List<BidAIAssessItemFactorDTO> assessItemFactor) {
		if (assessItemFactor == null || assessItemFactor.isEmpty()) {
			return null;
		}
		log.info("doubao大模型sdk招标要求内容提取--开始");
		// 招标文件内容
		String zbContent = PDFConvertStringUtil.parseByWordCom(zbpdf);
		// 评审规则表（招标部分）
		StringBuilder rules = new StringBuilder();
		rules.append("请解析下招标要求信息，招标文件内容如下：").append("\n");
		rules.append(zbContent).append("\n");
		rules.append("评审规则表如下:").append("\n");
		rules.append("|唯一标识|序号|审查点|评审要求|招标文件规定|").append("\n");
		rules.append("| ---- | ---- | ---- | ---- | ---- |").append("\n");
		for (BidAIAssessItemFactorDTO pfd : assessItemFactor) {
			rules.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder()).append("|" + pfd.getAssessItemName())
					.append("|" + pfd.getItemCriteria()).append("|" + pfd.getBidRequirements()).append("|")
					.append("\n");
		}
		ResultVO zbresult = VolcengineAiUtils.chatForRequireAgent(rules.toString());
		// md转对象
		List<Map<String, String>> requiremd = MarkdownUtil.md2List(zbresult.getData());
		// 解析后按标准格式返回
		List<BidAIAssessItemFactorDTO> lst = new ArrayList<>();
		for (Map<String, String> r : requiremd) {
			BidAIAssessItemFactorDTO p = new BidAIAssessItemFactorDTO();
			p.setItemGuid(r.get("唯一标识"));
			p.setItemOrder(r.get("序号"));
			p.setAssessItemName(r.get("审查点"));
			p.setItemCriteria(r.get("评审要求"));
			if("null".equals(r.get("招标文件规定")) || "无".equals(r.get("招标文件规定"))){
				p.setBidRequirements("");
			}else{
				p.setBidRequirements(r.get("招标文件规定"));
			}
			lst.add(p);
		}
		log.info("doubao大模型sdk招标要求内容提取--结束，" + lst.size() + "条");
		return new BidAIAssessItemFactorPack(zbresult.getInputTokens(), zbresult.getOutputTokens(), lst);
	}

	/**
	 * AI智能评标
	 * 
	 * @param assessItemFactor
	 *            评审规则表
	 * @param requirement
	 *            招标要求内容
	 * @param tenderFiles
	 *            投标响应材料
	 * @return
	 */
	@Override
	public BidAIEvaluationResultPack evaluation(List<BidAIAssessItemFactorDTO> assessItemFactor,
			List<BidAIRequirementDTO> requirement, List<BidAITenderFileDTO> tenderFiles, DomainType type) {
		if (assessItemFactor == null || tenderFiles == null || assessItemFactor.isEmpty() || tenderFiles.isEmpty()) {
			return null;
		}
		log.info("doubao大模型sdk AI智能评标--开始");
		// markdown整理出AI评审规则提示词
		StringBuilder psContent = new StringBuilder();
		psContent.append("评审规则表如下:").append("\n");
		psContent.append("|唯一标识|序号|审查点|评审要求|招标文件规定|投标响应情况|AI评审规则|AI评审理由|AI评审结论|").append("\n");
		psContent.append("| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |").append("\n");
		// 评标控制分批评分点和节点文件 防止输出过多
		for (BidAIAssessItemFactorDTO pfd : assessItemFactor) {
			psContent.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder())
					.append("|" + pfd.getAssessItemName()).append("|" + pfd.getItemCriteria())
					.append("|" + pfd.getBidRequirements()).append("|" + pfd.getTenderResponse())
					.append("|" + pfd.getAssessRules()).append("|").append("|").append("合格/不合格/提请人工判断|").append("\n");
		}
		// 提示词增加招标要求内容
		if (requirement != null && !requirement.isEmpty()) {
			psContent.append("已知招标文件中本项目的");
			for (BidAIRequirementDTO r : requirement) {
				psContent.append("“" + r.getRequireName() + "”是" + r.getRequireValue() + "，");
			}
			psContent.append("\n");
		}
		// 明确输出规范
		psContent.append("请开始评审。");
		// pdf转图片base64
		List<String> imgUrls = new ArrayList<>();
		for (BidAITenderFileDTO ff : tenderFiles) {
			imgUrls.add(ff.getFileContent());
		}

		// AI进行评审
		ResultVO result = null;
		if (type == DomainType.CG) {
			result = VolcengineAiUtils.chatForBidAgent_cg(psContent.toString(), imgUrls,
					"补充了部分投标文件材料，提取完善“投标响应情况”中缺失的关键信息后再次评审。");
		} else {
			result = VolcengineAiUtils.chatForBidAgent_gc(psContent.toString(), imgUrls,
					"补充了部分投标文件材料，提取完善“投标响应情况”中缺失的关键信息后再次评审。");
		}
		// md转对象，“字段”“内容”
		List<Map<String, String>> resultmd = MarkdownUtil.md2List(result.getData());
		// 解析后按标准格式返回
		List<BidAIEvaluationResultDTO> lst = new ArrayList<>();
		for (Map<String, String> r : resultmd) {
			if (StringUtils.isBlank(r.get("唯一标识")) || "无".equals(r.get("唯一标识"))) {
				continue;
			}
			BidAIEvaluationResultDTO p = new BidAIEvaluationResultDTO();
			p.setItemGuid(r.get("唯一标识"));
			p.setAssessItemName(r.get("审查点"));
			p.setItemCriteria(r.get("评审要求"));
			p.setBidRequirements(r.get("招标文件规定"));
			p.setTenderResponse(r.get("投标响应情况"));
			p.setReason(r.get("AI评审理由"));
			p.setResult(r.get("AI评审结论"));
			lst.add(p);
		}
		log.info("doubao大模型sdk AI智能评标--结束，" + lst.size() + "条");
		return new BidAIEvaluationResultPack(result.getInputTokens(), result.getOutputTokens(), lst);
	}

	@Override
	public BidAIEvaluationResultPack evaluationformark(List<BidAIAssessItemFactorDTO> assessItemFactor,
			List<BidAIRequirementDTO> requirement, List<BidAITenderFileDTO> tenderFiles, DomainType type) {
		if (assessItemFactor == null || tenderFiles == null || assessItemFactor.isEmpty() || tenderFiles.isEmpty()) {
			return null;
		}
		log.info("doubao大模型sdk AI智能评标_打分--开始");
		// 处理评审规则转成投标响应提取表
		StringBuilder tbxyContent = new StringBuilder();
		tbxyContent.append("评审规则表如下:").append("\n");
		tbxyContent.append("|唯一标识|序号|审查点|评审指标|投标响应情况|定位页码|").append("\n");
		tbxyContent.append("| ---- | ---- | ---- | ---- | ---- | ---- |").append("\n");
		for (BidAIAssessItemFactorDTO pfd : assessItemFactor) {
			String rule = (StringUtils.isBlank(pfd.getAssessRules()) || "/".equals(pfd.getAssessRules())) ? pfd.getItemCriteria() : pfd.getAssessRules();
			tbxyContent.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder())
					.append("|" + pfd.getAssessItemName()).append("|" + rule)
					.append("|" + pfd.getTenderResponse()).append("|").append("|").append("\n");
		}
		// 明确输出规范
		tbxyContent.append("请提取投标响应情况。");
		// pdf转图片base64
		List<String> imgUrls = new ArrayList<>();
		for (BidAITenderFileDTO ff : tenderFiles) {
			imgUrls.add(ff.getFileContent());
		}
		ResultVO tbxyresult = VolcengineAiUtils.chatForResponseAgent(tbxyContent.toString(), imgUrls);
		// 将投标响应情况放入评审表
		List<Map<String, String>> tbxy = MarkdownUtil.md2List(tbxyresult.getData());
		// markdown整理出AI评审规则提示词
		StringBuilder psContent = new StringBuilder();
		psContent.append("评审规则表如下:").append("\n");
		psContent.append("|唯一标识|序号|审查点|评审指标|分值|投标响应情况|总得分|评审意见|").append("\n");
		psContent.append("| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |").append("\n");
		// 评标控制分批评分点和节点文件 防止输出过多
		for (BidAIAssessItemFactorDTO pfd : assessItemFactor) {
			String rule = (StringUtils.isBlank(pfd.getAssessRules()) || "/".equals(pfd.getAssessRules())) ? pfd.getItemCriteria() : pfd.getAssessRules();
			Map<String, String> m = tbxy.stream().filter(p -> pfd.getItemGuid().equals(p.get("唯一标识"))).findFirst().orElse(new HashMap<>());
			psContent.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder())
					.append("|" + pfd.getAssessItemName()).append("|" + rule)
					.append("|" + pfd.getMark()).append("|" + m.get("投标响应情况")).append("|").append("|").append("|")
					.append("\n");
		}
		// 提示词增加招标要求内容
		if (requirement != null && !requirement.isEmpty()) {
			psContent.append("已知招标文件中本项目的");
			for (BidAIRequirementDTO r : requirement) {
				psContent.append("“" + r.getRequireName() + "”是" + r.getRequireValue() + "，");
			}
			psContent.append("\n");
		}
		// 明确输出规范
		psContent.append("请开始评审。");

		// AI进行评审
		ResultVO result = VolcengineAiUtils.chatForBidAgent_mark(psContent.toString());
		// md转对象，“字段”“内容”
		List<Map<String, String>> resultmd = MarkdownUtil.md2List(result.getData());
		// 解析后按标准格式返回
		List<BidAIEvaluationResultDTO> lst = new ArrayList<>();
		for (Map<String, String> r : resultmd) {
			if (StringUtils.isBlank(r.get("唯一标识")) || "无".equals(r.get("唯一标识"))) {
				continue;
			}
			Map<String, String> m = tbxy.stream().filter(p -> r.get("唯一标识").equals(p.get("唯一标识"))).findFirst().orElse(new HashMap<>());
			BidAIEvaluationResultDTO p = new BidAIEvaluationResultDTO();
			p.setItemGuid(r.get("唯一标识"));
			p.setAssessItemName(r.get("审查点"));
			p.setTenderResponse(r.get("投标响应情况"));
			p.setPosition(m.get("定位页码"));
			p.setReason(r.get("评审意见"));
			p.setResult(r.get("总得分"));
			lst.add(p);
		}
		log.info("doubao大模型sdk AI智能评标_打分--结束，" + lst.size() + "条");
		return new BidAIEvaluationResultPack(tbxyresult.getInputTokens() + result.getInputTokens(),
				tbxyresult.getOutputTokens() + result.getOutputTokens(), lst);
	}

	@Override
	public BidAIAssessItemFactorPack responseContentStripper(List<BidAIAssessItemFactorDTO> assessItemFactor,
			List<BidAITenderFileDTO> tenderFiles) {
		if (assessItemFactor == null || tenderFiles == null || assessItemFactor.isEmpty() || tenderFiles.isEmpty()) {
			return null;
		}
		log.info("doubao大模型sdk 提取投标响应情况--开始");
		// 处理评审规则转成投标响应提取表
		StringBuilder tbxyContent = new StringBuilder();
		tbxyContent.append("评审规则表如下:").append("\n");
		tbxyContent.append("|唯一标识|序号|审查点|评审指标|投标响应情况|定位页码|").append("\n");
		tbxyContent.append("| ---- | ---- | ---- | ---- | ---- | ---- |").append("\n");
		for (BidAIAssessItemFactorDTO pfd : assessItemFactor) {
			String rule = (StringUtils.isBlank(pfd.getAssessRules()) || "/".equals(pfd.getAssessRules())) ? pfd.getItemCriteria() : pfd.getAssessRules();
			tbxyContent.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder())
					.append("|" + pfd.getAssessItemName()).append("|" + rule)
					.append("|" + pfd.getTenderResponse()).append("|").append("|").append("\n");
		}
		// 明确输出规范
		tbxyContent.append("请提取投标响应情况。");
		// pdf转图片base64
		List<String> imgUrls = new ArrayList<>();
		for (BidAITenderFileDTO ff : tenderFiles) {
			imgUrls.add(ff.getFileContent());
		}
		ResultVO tbxyresult = VolcengineAiUtils.chatForResponseAgent(tbxyContent.toString(), imgUrls);
		// 将投标响应情况放入评审表
		List<Map<String, String>> tbxy = MarkdownUtil.md2List(tbxyresult.getData());
		// 评标控制分批评分点和节点文件 防止输出过多
		for (BidAIAssessItemFactorDTO pfd : assessItemFactor) {
			Map<String, String> m = tbxy.stream().filter(p -> pfd.getItemGuid().equals(p.get("唯一标识"))).findFirst().orElse(new HashMap<>());
			pfd.setTenderResponse(m.get("投标响应情况"));
			pfd.setPosition(m.get("定位页码"));
		}
		log.info("doubao大模型sdk 提取投标响应情况--结束，" + assessItemFactor.size() + "条");
		return new BidAIAssessItemFactorPack(tbxyresult.getInputTokens(), tbxyresult.getOutputTokens(),
				assessItemFactor);
	}

	@Override
	public BidAIEvaluationResultPack evaluation(List<BidAIAssessItemFactorDTO> assessItemFactor,
			List<BidAIRequirementDTO> requirement, DomainType type) {
		if (assessItemFactor == null || assessItemFactor.isEmpty()) {
			return null;
		}
		log.info("doubao大模型sdk AI智能评标--开始");
		// markdown整理出AI评审规则提示词
		StringBuilder psContent = new StringBuilder();
		psContent.append("评审规则表如下:").append("\n");
		psContent.append("|唯一标识|序号|审查点|评审要求|招标文件规定|投标响应情况|AI评审规则|AI评审理由|AI评审结论|").append("\n");
		psContent.append("| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |").append("\n");
		// 评标控制分批评分点和节点文件 防止输出过多
		for (BidAIAssessItemFactorDTO pfd : assessItemFactor) {
			psContent.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder())
					.append("|" + pfd.getAssessItemName()).append("|" + pfd.getItemCriteria())
					.append("|" + pfd.getBidRequirements()).append("|" + pfd.getTenderResponse())
					.append("|" + pfd.getAssessRules()).append("|").append("|").append("合格/不合格/提请人工判断|").append("\n");
		}
		// 提示词增加招标要求内容
		if (requirement != null && !requirement.isEmpty()) {
			psContent.append("已知招标文件中本项目的");
			for (BidAIRequirementDTO r : requirement) {
				psContent.append("“" + r.getRequireName() + "”是" + r.getRequireValue() + "，");
			}
			psContent.append("\n");
		}
		// 明确输出规范
		psContent.append("请开始评审。");

		// AI进行评审
		ResultVO result = VolcengineAiUtils.chatForBidAgent_fhx(psContent.toString());
		// md转对象，“字段”“内容”
		List<Map<String, String>> resultmd = MarkdownUtil.md2List(result.getData());
		// 解析后按标准格式返回
		List<BidAIEvaluationResultDTO> lst = new ArrayList<>();
		for (Map<String, String> r : resultmd) {
			if (StringUtils.isBlank(r.get("唯一标识")) || "无".equals(r.get("唯一标识"))) {
				continue;
			}
			BidAIEvaluationResultDTO p = new BidAIEvaluationResultDTO();
			p.setItemGuid(r.get("唯一标识"));
			p.setAssessItemName(r.get("审查点"));
			p.setTenderResponse(r.get("投标响应情况"));
			p.setReason(r.get("AI评审理由"));
			p.setResult(r.get("AI评审结论"));
			lst.add(p);
		}
		log.info("doubao大模型sdk AI智能评标--结束，" + lst.size() + "条");
		return new BidAIEvaluationResultPack(result.getInputTokens(), result.getOutputTokens(), lst);
	}
	
	@Override
	public BidAIHorizontalEvalPack tenderHorizontalEval(BidAIAssessItemFactorDTO assessItemFactor, List<BidAIRequirementDTO> requirement, DomainType type, List<BidAIHorizontalEvalDTO> preAIEvaluations) {
		return null;
	}
}
