package com.epoint.ebpu.AIClientSDK.util;

import java.io.InputStream;
import java.util.*;

import org.apache.commons.lang3.StringUtils;

import com.epoint.ebpu.AIClientSDK.client.MoonshotAiUtils;
import com.epoint.wordcom.bean.NodeBean;
import com.epoint.wordcom.bean.NodeType;
import com.epoint.wordcom.bean.ParagraphBean;
import com.epoint.wordcom.bean.Table;
import com.epoint.wordcom.bean.TableCell;
import com.epoint.wordcom.bean.TableRow;
import com.epoint.wordcom.parse.PdfNodeParser;

public class PDFConvertStringUtil {

	public static String ocrByKimi(InputStream zbpdf, String filename) {
		String zbpdfContent = "";
		Map<String, Object> file = MoonshotAiUtils.uploadMSAiFiles(zbpdf, filename);
		Map<String, Object> fileContent = MoonshotAiUtils.fileContent(String.valueOf(file.get("id")));
		if (fileContent != null && fileContent.size() > 0
				&& StringUtils.isNotBlank((String) fileContent.get("content"))) {
			zbpdfContent = (String) fileContent.get("content");
		} else {
			System.out.println("招标文件内容为空");
		}
		return zbpdfContent;
	}

	public static String parseByWordCom(InputStream zbpdf) {
		PdfNodeParser s = new PdfNodeParser(zbpdf);
		List<NodeBean> list = s.findParagraphs();
		StringBuilder sb = new StringBuilder();
		for (NodeBean n : list) {
			if (NodeType.PARAGRAPH.equals(n.getType())) {
				ParagraphBean p = (ParagraphBean) n.getNode();
				if (p.isOwnTitle()) {
					String level = p.getTitle().entrySet().stream()
							.filter(entry -> p.getText().equals(entry.getValue())).map(Map.Entry::getKey).findFirst()
							.get();
					if ("1".equals(level)) {
						sb.append("# " + p.getText() + "\n");
					} else if ("2".equals(level)) {
						sb.append("## " + p.getText() + "\n");
					} else {
						sb.append("### " + p.getText() + "\n");
					}
				} else {
					sb.append(p.getText() + "\n");
				}
			} else if (NodeType.TABLE.equals(n.getType())) {
				Table t = (Table) n.getNode();
				for (int i = 0; i < t.getRows().size(); i++) {
					TableRow r = t.getRows().get(i);
					for (int j = 0; j < t.getColCount(); j++) {
						TableCell c = r.find(j);
						sb.append("|" + (c == null ? "" : c.getText()));
					}
					sb.append("|\n");
					if (i == 0) {
						for (int j = 0; j < t.getColCount(); j++) {
							sb.append("| --- ");
						}
						sb.append("|\n");
					}
				}
				sb.append("\n");
			}
		}
		return sb.toString();
	}

	/**
	 * 按照限制长度切割为多个段落内容
	 *
	 * @param zbpdf
	 * @param limitParagraphLenth
	 * @return
	 */
	public static List<String> parseParagraphsByWordCom(InputStream zbpdf, int limitParagraphLenth) {
		PdfNodeParser s = new PdfNodeParser(zbpdf);
		List<NodeBean> list = s.findParagraphs();
		int cntLength=0;
		List<String> results=new ArrayList<>();
		StringBuilder sb_LimitP=new StringBuilder();
		for (NodeBean n : list) {
			StringBuilder sb = new StringBuilder();
			if (NodeType.PARAGRAPH.equals(n.getType())) {
				ParagraphBean p = (ParagraphBean) n.getNode();
				if (p.isOwnTitle()) {
					String level = p.getTitle().entrySet().stream()
							.filter(entry -> p.getText().equals(entry.getValue())).map(Map.Entry::getKey).findFirst()
							.get();
					if ("1".equals(level)) {
						sb.append("# " + p.getText() + "\n");
					} else if ("2".equals(level)) {
						sb.append("## " + p.getText() + "\n");
					} else {
						sb.append("### " + p.getText() + "\n");
					}
				} else {
					sb.append(p.getText() + "\n");
				}
			} else if (NodeType.TABLE.equals(n.getType())) {
				Table t = (Table) n.getNode();
				for (int i = 0; i < t.getRows().size(); i++) {
					TableRow r = t.getRows().get(i);
					for (int j = 0; j < t.getColCount(); j++) {
						TableCell c = r.find(j);
						sb.append("|" + (c == null ? "" : c.getText()));
					}
					sb.append("|\n");
					if (i == 0) {
						for (int j = 0; j < t.getColCount(); j++) {
							sb.append("| --- ");
						}
						sb.append("|\n");
					}
				}
				sb.append("\n");
			}

			//判断长度，是否达到传入限制阈值
			if(sb_LimitP.length()>0&&(sb_LimitP.length()+sb.length()>limitParagraphLenth)){
				results.add(sb_LimitP.toString());
				sb_LimitP=new StringBuilder();
			}
			sb_LimitP.append(sb);
		}

		if(sb_LimitP.length()>0){
			results.add(sb_LimitP.toString());
		}
		return results;
	}

	/**
	 * 按照限制长度切割为多个段落内容
	 *
	 * @param zbpdf
	 * @param limitParagraphLenth
	 * @return
	 */
	@Deprecated
	public static List<String> parseParagraphsByWordCom_ByTitle(InputStream zbpdf, int limitParagraphLenth) {
		// key为：当前段落结尾的长度位置，value为当前开始段落的内容
		Map<Integer, StringBuilder> map = new LinkedHashMap<>();

		PdfNodeParser s = new PdfNodeParser(zbpdf);
		List<NodeBean> list = s.findParagraphs();
		StringBuilder sb_Now = new StringBuilder();// 目前的
		int cntLength = 0;
		for (NodeBean n : list) {
			if (NodeType.PARAGRAPH.equals(n.getType())) {
				ParagraphBean p = (ParagraphBean) n.getNode();
				if (p.isOwnTitle()) {
					String level = p.getTitle().entrySet().stream()
							.filter(entry -> p.getText().equals(entry.getValue())).map(Map.Entry::getKey).findFirst()
							.get();
					// 有标题就作为分割
					if (sb_Now.length() != 0) {
						cntLength += sb_Now.length();
						map.put(cntLength, sb_Now);
						sb_Now = new StringBuilder();
					}
					if ("1".equals(level)) {
						sb_Now.append("# " + p.getText() + "\n");
					} else if ("2".equals(level)) {
						sb_Now.append("## " + p.getText() + "\n");
					} else {
						sb_Now.append("### " + p.getText() + "\n");
					}
				} else {
					sb_Now.append(p.getText() + "\n");
				}
			} else if (NodeType.TABLE.equals(n.getType())) {
				Table t = (Table) n.getNode();
				for (int i = 0; i < t.getRows().size(); i++) {
					TableRow r = t.getRows().get(i);
					for (int j = 0; j < t.getColCount(); j++) {
						TableCell c = r.find(j);
						sb_Now.append("|" + (c == null ? "" : c.getText()));
					}
					sb_Now.append("|\n");
					if (i == 0) {
						for (int j = 0; j < t.getColCount(); j++) {
							sb_Now.append("| --- ");
						}
						sb_Now.append("|\n");
					}
				}
				sb_Now.append("\n");
			}
		}

		if (sb_Now.length() != 0) {
			cntLength += sb_Now.length();
			map.put(cntLength, sb_Now);
		}
		// 按照给度长度进行分割
		StringBuilder sbJoint = new StringBuilder();
		Iterator<Map.Entry<Integer, StringBuilder>> iterator = map.entrySet().iterator();
		List<String> ret = new ArrayList<>();
		while (iterator.hasNext()) {
			// 如果加上当前的就超了，则需要先塞进来
			StringBuilder nowSB = iterator.next().getValue();
			if ((sbJoint.length() + nowSB.length() > limitParagraphLenth) && sbJoint.length() > 0) {
				ret.add(sbJoint.toString());
				sbJoint = new StringBuilder();
			}
			sbJoint.append(nowSB);
		}
		if (sbJoint.length() > 0) {
			ret.add(sbJoint.toString());
		}

		return ret;
	}
}
