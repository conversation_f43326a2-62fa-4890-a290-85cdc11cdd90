package com.epoint.ebpu.AIClientSDK.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.epoint.ebpu.AIClientSDK.api.IPBToEpAgentService;
import com.epoint.ebpu.AIClientSDK.api.enums.BidPFDType;
import com.epoint.ebpu.AIClientSDK.api.factory.AiPBFactory;
import com.epoint.ebpu.AIClientSDK.client.QwenAiUtils;
import com.epoint.ebpu.AIClientSDK.controller.dto.*;
import com.epoint.ebpu.AIClientSDK.controller.returndata.ReturnDataVO;
import com.epoint.ebpu.AIClientSDK.dto.*;
import com.epoint.ebpu.AIClientSDK.service.AIBidClientForQwen;
import com.epoint.ebpu.AIClientSDK.util.AIStringUtil;
import com.epoint.ebpu.AIClientSDK.util.MarkdownUtil;
import com.epoint.ebpu.AIClientSDK.util.PDFConvertStringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-24
 * @Version
 * @Description
 */
@Slf4j
@RestController
@RequestMapping("/aiagent")
public class EpAiAgentController {
	@Autowired
	protected AiPBFactory aiPBFactory;

	/**
	 * 【Z1】招标文件解析段落获取接口
	 *
	 * @param json
	 * @return
	 */
	@PostMapping("/zbfileparsing")
	public String zbfileparsing(@RequestBody String json) {
		log.info("【Z1】zbfileparsing（招标文件解析段落获取接口）输入:{}",json);
		IPBToEpAgentService pbToEpAgentService = aiPBFactory.getPBToEpAgentService();

		ReqZbfileparsingDTO reqZbfileparsingDTO = JSONObject.parseObject(json, ReqZbfileparsingDTO.class);
		if (!pbToEpAgentService.validateToken(reqZbfileparsingDTO.getPlatformcode(),reqZbfileparsingDTO.getAccesstoken())) {
			return ReturnDataVO.Fail("token校验失败");
		}
		InputStream zbFileInputStream = pbToEpAgentService.getZBFileInputStream(reqZbfileparsingDTO.getPlatformcode(),
				reqZbfileparsingDTO.getBidsectionguid());

		List<ZbfileparsingDTO.Paragraphs> rets = new ArrayList<>();
		// 切割为多个段落
		double length= (32-8-6)*1024/0.7;
		List<String> paragraphs = PDFConvertStringUtil.parseParagraphsByWordCom(zbFileInputStream, (int)length);
		for (String string : paragraphs) {
			rets.add(new ZbfileparsingDTO.Paragraphs(string, (int) (string.length() * 0.7)));// 大概给个值
		}
		ZbfileparsingDTO zbfileparsingDTO = new ZbfileparsingDTO();
		zbfileparsingDTO.setParagraphs(rets);

		log.info("【Z1】zbfileparsing（招标文件解析段落获取接口）输出:{}", JSON.toJSON(zbfileparsingDTO));
		return ReturnDataVO.SUCCESS(zbfileparsingDTO);
	}

	/**
	 * 【Z2】招标要求信息表获取接口
	 *
	 * @param json
	 * @return
	 */
	@PostMapping("/getzbrequirementtable")
	public String getzbrequirementtable(@RequestBody String json) {
		log.info("【Z2】getzbrequirementtable（招标要求信息表获取接口）输入:{}",json);
		IPBToEpAgentService pbToEpAgentService = aiPBFactory.getPBToEpAgentService();
		ReqGetzbrequirementtableDTO requestDTO = JSONObject.parseObject(json, ReqGetzbrequirementtableDTO.class);
		if (!pbToEpAgentService.validateToken(requestDTO.getPlatformcode(),requestDTO.getAccesstoken())) {
			return ReturnDataVO.Fail("token校验失败");
		}
		BidPFDType useType = BidPFDType.getByPFDType(requestDTO.getUsetype());
		List<BidAIAssessItemFactorDTO> aiAssessItemtable = pbToEpAgentService
				.getAssessItemFactor4Require(requestDTO.getPlatformcode(), requestDTO.getBidsectionguid(), useType);
		// 评审规则表（招标部分）
		StringBuilder rules = new StringBuilder();
		rules.append("评审规则表如下:").append("\n");
		rules.append("|唯一标识|序号|审查点|评审要求|招标文件规定|").append("\n");
		rules.append("| ---- | ---- | ---- | ---- | ---- |").append("\n");
		for (BidAIAssessItemFactorDTO pfd : aiAssessItemtable) {
			rules.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder()).append("|" + pfd.getAssessItemName())
					.append("|" + AIStringUtil.dealEmptyStr2Model(pfd.getItemCriteria()))
					.append("|" + AIStringUtil.dealEmptyStr2Model(pfd.getBidRequirements())).append("|").append("\n");
		}
		GetzbrequirementtableDTO getzbrequirementtableDTO = new GetzbrequirementtableDTO();
		getzbrequirementtableDTO.setZbrequirementtable(rules.toString());
		log.info("【Z2】getzbrequirementtable（招标要求信息表获取接口）输出:{}",JSON.toJSON(getzbrequirementtableDTO));
		return ReturnDataVO.SUCCESS(getzbrequirementtableDTO);
	}

	/**
	 * 【Z3】招标要求信息结果推送接口
	 *
	 * @param json
	 * @return
	 */
	@PostMapping("/pushzbrequirementresult")
	public String pushzbrequirementresult(@RequestBody String json) {
		log.info("【Z3】pushzbrequirementresult（招标要求信息结果推送接口）输入:{}",json);
		IPBToEpAgentService pbToEpAgentService = aiPBFactory.getPBToEpAgentService();
		ReqPushzbrequirementresultDTO requestDTO = JSONObject.parseObject(json, ReqPushzbrequirementresultDTO.class);
		if (!pbToEpAgentService.validateToken(requestDTO.getPlatformcode(),requestDTO.getAccesstoken())) {
			return ReturnDataVO.Fail("token校验失败");
		}

		// md转对象
		List<Map<String, String>> requiremd = MarkdownUtil.md2List(requestDTO.getResult());
		// 解析后按标准格式返回
		List<BidAIAssessItemFactorDTO> lst = new ArrayList<>();
		for (Map<String, String> r : requiremd) {
			BidAIAssessItemFactorDTO p = new BidAIAssessItemFactorDTO();
			p.setItemGuid(r.get("唯一标识"));
			p.setItemOrder(r.get("序号"));
			p.setAssessItemName(r.get("审查点"));
			p.setItemCriteria(AIStringUtil.dealEmptyStr(r.get("评审要求")));
			p.setBidRequirements(AIStringUtil.dealEmptyStr(r.get("招标文件规定")));
			lst.add(p);
		}
		pbToEpAgentService.pushzbrequirementresult(requestDTO.getRequestid_agent(), requestDTO.getPlatformcode(),
				requestDTO.getBidsectionguid(), lst);

		log.info("【Z3】pushzbrequirementresult（招标要求信息结果推送接口）输出:{}","执行结束");
		return ReturnDataVO.SUCCESS();
	}

	/**
	 * 【T1】投标文件解析切图接口
	 *
	 * @param json
	 * @return
	 */
	@PostMapping("/tbfileparsing")
	public String tbfileparsing(@RequestBody String json) {
		log.info("【T1】tbfileparsing（投标文件解析切图接口）输入:{}",json);
		IPBToEpAgentService pbToEpAgentService = aiPBFactory.getPBToEpAgentService();
		ReqTbfileparsingDTO requestDTO = JSONObject.parseObject(json, ReqTbfileparsingDTO.class);
		if (!pbToEpAgentService.validateToken(requestDTO.getPlatformcode(),requestDTO.getAccesstoken())) {
			return ReturnDataVO.Fail("token校验失败");
		}
		// 评标提供处理后的投标节点的图片base64或默认文件路径（智能体平台实现下载或相对路径读盘）
		List<BidAITenderFileDTO> tenderFiles = pbToEpAgentService.getTenderFiles(requestDTO.getPlatformcode(),
				requestDTO.getBidsectionguid(), requestDTO.getTenderguid(), requestDTO.getBidassessitemguids(),
				"2".equals(requestDTO.getGetpicway()));
		TbfileparsingDTO tbfileparsingDTO = new TbfileparsingDTO();
		tbfileparsingDTO.setTbfilepic_everytokens("-1");
		if ("1".equals(requestDTO.getGetpicway())) {
			tbfileparsingDTO.setTbfilepic_localpath(
					tenderFiles.stream().map(BidAITenderFileDTO::getFilePath).collect(Collectors.toList()));
		} else {
			tbfileparsingDTO.setTbfilepic_localpath(
					tenderFiles.stream().map(BidAITenderFileDTO::getFileContent).collect(Collectors.toList()));
		}

		log.info("【T1】tbfileparsing（投标文件解析切图接口）输出图片路径:{}",JSON.toJSON(tbfileparsingDTO.getTbfilepic_localpath()));
		return ReturnDataVO.SUCCESS(tbfileparsingDTO);
	}

	/**
	 * 【T2】评审表获取接口
	 *
	 * @param json
	 * @return
	 */
	@PostMapping("/getassesstable")
	public String getassesstable(@RequestBody String json) {
		log.info("【T2】getassesstable（评审表获取接口）输入:{}",json);
		IPBToEpAgentService pbToEpAgentService = aiPBFactory.getPBToEpAgentService();
		ReqGetassesstableDTO requestDTO = JSONObject.parseObject(json, ReqGetassesstableDTO.class);
		if (!pbToEpAgentService.validateToken(requestDTO.getPlatformcode(),requestDTO.getAccesstoken())) {
			return ReturnDataVO.Fail("token校验失败");
		}
		List<BidAIAssessItemFactorDTO> assessItemFactor4Assess = pbToEpAgentService.getTenderItemFactor4Assess(
				requestDTO.getPlatformcode(), requestDTO.getBidsectionguid(),requestDTO.getTenderguid(),
				requestDTO.getBidassessitemguids());

		StringBuilder psContent = new StringBuilder();

		if("fhx".equals(requestDTO.getUsetype())){
			psContent.append("评审规则表如下:").append("\n");
			// 评审规则表
			psContent.append("|唯一标识|序号|审查点|评审要求|招标文件规定|投标响应情况|AI评审规则|AI评审理由|AI评审结论|").append("\n");
			psContent.append("| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |").append("\n");
			// 评标控制分批评分点和节点文件 防止输出过多
			for (BidAIAssessItemFactorDTO pfd : assessItemFactor4Assess) {
				psContent.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder())
						.append("|" + pfd.getAssessItemName()).append("|" + pfd.getItemCriteria())
						.append("|" + pfd.getBidRequirements()).append("|" + pfd.getTenderResponse())
						.append("|" + AIStringUtil.dealEmptyStr2Model(pfd.getAssessRules())).append("|").append("|")
						.append("合格/不合格/提请人工判断|").append("\n");
			}
		}
		else if("mark".equals(requestDTO.getUsetype())){
			psContent.append("响应情况提取规则表如下:").append("\n");
			psContent.append("|唯一标识|序号|审查点|评审指标|招标文件规定|投标响应情况|定位页码|").append("\n");
			psContent.append("| ---- | ---- | ---- | ---- | ---- | ---- | ---- |").append("\n");
			// 评标控制分批评分点和节点文件 防止输出过多
			for (BidAIAssessItemFactorDTO pfd : assessItemFactor4Assess) {
				psContent.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder())
						.append("|" + pfd.getAssessItemName()).append("|" + pfd.getItemCriteria()).append("|" + pfd.getBidRequirements())
						.append("| 响应度说明："+pfd.getTenderResponse()).append("，优点：{填充}，缺点：{填充}|").append("|").append("\n");
			}
		}
		else {
			throw new RuntimeException("未支持的评审类型");
		}

		StringBuilder zbRequirement = getZBRequirement(requestDTO.getPlatformcode(), requestDTO.getBidsectionguid());
		psContent.append(zbRequirement);
		// 明确输出规范
		psContent.append("请开始评审，按markdown格式输出评审表。");

		GetassesstableDTO getassesstable = new GetassesstableDTO();
		getassesstable.setAssesstable(psContent.toString());

		log.info("【T2】getassesstable（评审表获取接口）输出:{}", JSON.toJSON(getassesstable));
		return ReturnDataVO.SUCCESS(getassesstable);
	}

	/**
	 * 【T3】评审结果推送接口
	 *
	 * @param json
	 * @return
	 */
	@PostMapping("/pushassessresult")
	public String pushassessresult(@RequestBody String json) {
		log.info("【T3】pushassessresult（评审结果推送接口）输入:{}",json);
		IPBToEpAgentService pbToEpAgentService = aiPBFactory.getPBToEpAgentService();
		ReqPushassessresultDTO requestDTO = JSONObject.parseObject(json, ReqPushassessresultDTO.class);
		if (!pbToEpAgentService.validateToken(requestDTO.getPlatformcode(),requestDTO.getAccesstoken())) {
			return ReturnDataVO.Fail("token校验失败");
		}
		// md转对象，“字段”“内容”
		List<Map<String, String>> resultmd = MarkdownUtil.md2List(requestDTO.getResult());
		// 解析后按标准格式返回
		List<BidAIEvaluationResultDTO> lst = new ArrayList<>();
		for (Map<String, String> r : resultmd) {
			BidAIEvaluationResultDTO p = new BidAIEvaluationResultDTO();
			p.setItemGuid(r.get("唯一标识"));
			p.setAssessItemName(r.get("审查点"));
			p.setItemCriteria(r.get("评审要求"));
			p.setBidRequirements(r.get("招标文件规定"));
			p.setTenderResponse(r.get("投标响应情况"));
			p.setReason(r.get("AI评审理由"));
			p.setResult(r.get("AI评审结论"));
			lst.add(p);
		}
		//横向评审点不返回AI评审理由，AI评审结论
		BidPFDType usetype = BidPFDType.getByPFDType(requestDTO.getUsetype());
		if(BidPFDType.MARK.equals(usetype)){
			//查询评标中的评分点信息
			List<String> itemguids = lst.stream().map(BidAIEvaluationResultDTO::getItemGuid).collect(Collectors.toList());
			List<BidAIAssessItemFactorDTO> pbItemGuidInfos =
					pbToEpAgentService.getTenderItemFactor4Assess(requestDTO.getPlatformcode(),
							requestDTO.getBidsectionguid(),requestDTO.getTenderguid(),itemguids);

			for (BidAIEvaluationResultDTO bidAIEvaluationResultDTO : lst) {
				if(pbItemGuidInfos.stream().filter(k->k.getItemGuid().equals(bidAIEvaluationResultDTO.getItemGuid()))
						.findFirst().orElse(new BidAIAssessItemFactorDTO()).isHorizontal()){
					bidAIEvaluationResultDTO.setReason("");
					bidAIEvaluationResultDTO.setResult("");
				}
			}
		}
		pbToEpAgentService.pushassessresult(requestDTO.getRequestid_agent(), requestDTO.getPlatformcode(),
				requestDTO.getBidsectionguid(), requestDTO.getTenderguid(), lst);

		log.info("【T3】pushassessresult（评审结果推送接口）输出:{}","执行完成");
		return ReturnDataVO.SUCCESS();
	}

	/**
	 * 【J1】获取技术完整打分场景下评审表
	 *
	 * @param json
	 * @return
	 */
	@PostMapping("/getcompeltemarktable")
	public String getcompeltemarktable(@RequestBody String json) {
		log.info("【J1】getcompeltemarktable（获取技术完整打分场景下评审表）输入:{}",json);
		IPBToEpAgentService pbToEpAgentService = aiPBFactory.getPBToEpAgentService();
		ReqGetcompeltemarktableDTO requestDTO = JSONObject.parseObject(json, ReqGetcompeltemarktableDTO.class);
		if (!pbToEpAgentService.validateToken(requestDTO.getPlatformcode(),requestDTO.getAccesstoken())) {
			return ReturnDataVO.Fail("token校验失败");
		}

		List<Map<String, String>> mapsStriped=new ArrayList<>();
		//分为多轮获取响应情况
		for (int i = 0; i < requestDTO.getRoundsresult().size(); i++) {
			//第一轮直接全塞
			if(i==0){
				mapsStriped = MarkdownUtil.md2List(requestDTO.getRoundsresult().get(i));
				for (Map<String, String> mapStriped : mapsStriped) {
					StringBuilder stringBuilder=new StringBuilder();
					StringBuilder sB_Cut=new StringBuilder();
					String xyqk_Now = mapStriped.getOrDefault("投标响应情况", "");
					mapStriped.put("投标响应情况_全文",stringBuilder.append("第").append(i+1).append("轮提取情况--").append(xyqk_Now).toString());
					//定位页码、投标响应情况_截取处理
					if(StringUtils.isNotBlank(mapStriped.getOrDefault("定位页码", ""))
							&&!"无".equals(mapStriped.getOrDefault("定位页码", ""))){
						mapStriped.put("定位页码",mapStriped.get("定位页码"));
						//截取响应情况，用户不需要看到每一轮的优点，缺点
						mapStriped.put("投标响应情况",
								sB_Cut.append("第").append(i+1).append("轮提取情况--").append(new AIBidClientForQwen().getMiddleStr(xyqk_Now,
										"响应度说明","优点")).toString());
					}
					else {
						mapStriped.put("定位页码",mapStriped.get(""));
						mapStriped.put("投标响应情况","");
					}
				}
			}
			else {
				//将提取完的信息加到mapsStriped
				List<Map<String, String>> mapsInvoke = MarkdownUtil.md2List(requestDTO.getRoundsresult().get(i));
				for (Map<String, String> mapStriped : mapsStriped) {
					Optional<Map<String, String>> first = mapsInvoke.stream().filter(p -> p.get("唯一标识").equals(mapStriped.get("唯一标识"))).findFirst();
					if(first.isPresent()){
						String xyqk_Now=first.get().get("投标响应情况");
						StringBuilder stringBuilder = new StringBuilder(mapStriped.get("投标响应情况_全文"));
						StringBuilder sB_Cut=new StringBuilder(mapStriped.getOrDefault("投标响应情况",""));
						if(!StringUtils.endsWith(mapStriped.get("投标响应情况_全文"),"。")){
							stringBuilder.append("。");
							sB_Cut.append("。");//情况和投标响应情况一样的，就不拆分情况了
						}
						stringBuilder.append("第").append(i+1).append("轮提取情况--").append(xyqk_Now);

						mapStriped.put("投标响应情况_全文", stringBuilder.toString());
						//定位页面处理
						String dwym_Now = new AIBidClientForQwen().findAndAddNum(first.get().getOrDefault("定位页码", ""), i * QwenAiUtils.IMAGELIMITNUM_MODEL, i * QwenAiUtils.IMAGELIMITNUM_MODEL);
						// 页码为无，说明未提取到，直接跳过
						if(!"无".equals(dwym_Now)){
							mapStriped.put("定位页码",mapStriped.get("定位页码")+","+dwym_Now);
							mapStriped.put("投标响应情况",sB_Cut.append("第").append(i+1).append("轮提取情况--").append(new AIBidClientForQwen().getMiddleStr(xyqk_Now,"响应度说明","优点")).toString());
						}
					}
				}
			}
		}

		List<BidAIAssessItemFactorDTO> pbItemGuidInfos =
				pbToEpAgentService.getTenderItemFactor4Assess(requestDTO.getPlatformcode(),
						requestDTO.getBidsectionguid(),requestDTO.getTenderguid(),requestDTO.getBidassessitemguids());

		StringBuilder psContent_Assess = new StringBuilder();
		mapsStriped.forEach(p->{
			Optional<BidAIAssessItemFactorDTO> assessItem = pbItemGuidInfos.stream().filter(k -> k.getItemGuid().equals(p.get("唯一标识"))).findFirst();
			p.remove("定位页码");
			p.remove("投标响应情况_全文");
			p.put("招标文件规定",assessItem.orElseGet(BidAIAssessItemFactorDTO::new).getBidRequirements());
			p.put("AI评审规则",assessItem.orElseGet(BidAIAssessItemFactorDTO::new).getAssessRules());
			p.put("分数上限",assessItem.orElseGet(BidAIAssessItemFactorDTO::new).getMark());
			p.put("得分","");
			p.put("得分理由","");
		});
		psContent_Assess.append("响应情况打分表内容:").append(MarkdownUtil.list2MD(mapsStriped)).append("\n ");

		StringBuilder zbRequirement = getZBRequirement(requestDTO.getPlatformcode(), requestDTO.getBidsectionguid());
		psContent_Assess.append(zbRequirement);

		GetcompeltemarktableDTO getcompeltemarktableDTO = new GetcompeltemarktableDTO();
		getcompeltemarktableDTO.setCompeltemarktable(psContent_Assess.toString());

		log.info("【J1】getcompeltemarktable（获取技术完整打分场景下评审表）输出:{}",JSON.toJSON(getcompeltemarktableDTO));
		return ReturnDataVO.SUCCESS(getcompeltemarktableDTO);
	}

	/**
	 * 【H1】横向评审打分表获取接口
	 *
	 * @param json
	 * @return
	 */
	@PostMapping("/gethorizontalmarktable")
	public String gethorizontalmarktable(@RequestBody String json) {
		log.info("【H1】gethorizontalmarktable（横向评审打分表获取接口）输入:{}",json);
		IPBToEpAgentService pbToEpAgentService = aiPBFactory.getPBToEpAgentService();
		ReqGethorizontalmarktableDTO requestDTO = JSONObject.parseObject(json, ReqGethorizontalmarktableDTO.class);
		if (!pbToEpAgentService.validateToken(requestDTO.getPlatformcode(),requestDTO.getAccesstoken())) {
			return ReturnDataVO.Fail("token校验失败");
		}

		// 提示词增加招标要求内容
		StringBuilder zbRequirement = getZBRequirement(requestDTO.getPlatformcode(), requestDTO.getBidsectionguid());

		BidAIAssessItemFactorDTO pbItemGuidInfo =
				pbToEpAgentService.getCommonItemFactor4Assess(requestDTO.getPlatformcode(),
						requestDTO.getBidsectionguid(),Arrays.asList(requestDTO.getBidassessitemguid())).get(0);
		StringBuilder psContent_Assess = new StringBuilder("单位横向评审表内容：");
		psContent_Assess.append("# 审查点内容及规则\n")
				.append("审查点：").append(pbItemGuidInfo.getAssessItemName()).append("\n")
				.append("评审指标：").append(pbItemGuidInfo.getItemCriteria()).append("\n")
				.append("AI评审规则：").append(pbItemGuidInfo.getAssessRules()).append("\n")
				.append("招标文件规定：").append(pbItemGuidInfo.getBidRequirements()).append("\n")
		;

		List<BidAIHorizontalEvalDTO> tenders = pbToEpAgentService.getAIIHorizontalEvalTenders(requestDTO.getPlatformcode(), requestDTO.getBidsectionguid(),
				pbItemGuidInfo);
		List<Map<String, String>> mapsToEval=new ArrayList<>();
		for (BidAIHorizontalEvalDTO preAIEvaluation : tenders) {
			Map<String, String> map = new LinkedHashMap<>();
			map.put("单位唯一标识", preAIEvaluation.getTenderGuid());
			map.put("投标响应情况", preAIEvaluation.getTenderResponse());
			map.put("分数上限", pbItemGuidInfo.getMark());
			map.put("总得分", "");
			map.put("得分理由", "");
			mapsToEval.add(map);
		}
		psContent_Assess.append("\n# 评审表\b").append(MarkdownUtil.list2MD(mapsToEval));

		//拼接打分内容
		psContent_Assess.append(zbRequirement).append("请开始提取，按markdown格式输出单位横向评审结果表。");

		GethorizontalmarktableDTO gethorizontalmarktableDTO = new GethorizontalmarktableDTO();
		gethorizontalmarktableDTO.setAssesstable(psContent_Assess.toString());

		log.info("【H1】gethorizontalmarktable（横向评审打分表获取接口）输出:{}",JSON.toJSON(gethorizontalmarktableDTO));
		return ReturnDataVO.SUCCESS(gethorizontalmarktableDTO);
	}

	/**
	 * 【H9】横向评审结果推送接口
	 *
	 * @param json
	 * @return
	 */
	@PostMapping("/pushhorizontalresult")
	public String pushhorizontalresult(@RequestBody String json) {
		log.info("【H9】pushhorizontalresult（横向评审结果推送接口）输入:{}",json);
		IPBToEpAgentService pbToEpAgentService = aiPBFactory.getPBToEpAgentService();
		ReqPushhorizontalresultDTO requestDTO = JSONObject.parseObject(json, ReqPushhorizontalresultDTO.class);
		if (!pbToEpAgentService.validateToken(requestDTO.getPlatformcode(),requestDTO.getAccesstoken())) {
			return ReturnDataVO.Fail("token校验失败");
		}

		BidAIAssessItemFactorDTO pbItemGuidInfo =
				pbToEpAgentService.getCommonItemFactor4Assess(requestDTO.getPlatformcode(),
						requestDTO.getBidsectionguid(),Arrays.asList(requestDTO.getBidassessitemguid())).get(0);

		List<Map<String, String>> mapsPSResult = MarkdownUtil.md2List(requestDTO.getResult());
		List<BidAIHorizontalEvalDTO> evalDTOS = mapsPSResult.stream().map(map -> {
			BidAIHorizontalEvalDTO evalDTO = new BidAIHorizontalEvalDTO();
			evalDTO.setTenderGuid(map.get("单位唯一标识"));
			evalDTO.setItemGuid(pbItemGuidInfo.getItemGuid());
			evalDTO.setAssessItemName(pbItemGuidInfo.getAssessItemName());
			evalDTO.setResult(map.get("总得分"));
			evalDTO.setReason(map.get("得分理由"));
			return evalDTO;
		}).collect(Collectors.toList());

		pbToEpAgentService.pushhorizontalresult(requestDTO.getRequestid_agent(),requestDTO.getPlatformcode(),
				requestDTO.getBidsectionguid(),
				requestDTO.getBidassessitemguid(),evalDTOS);

		log.info("【H9】pushhorizontalresult（横向评审结果推送接口）输出:{}","执行完成");
		return ReturnDataVO.SUCCESS();
	}

	protected StringBuilder getZBRequirement(String platformcode,String bidsectionguid){
		IPBToEpAgentService pbToEpAgentService = aiPBFactory.getPBToEpAgentService();
		List<BidAIRequirementDTO> requirement = pbToEpAgentService.getZBRequirement(platformcode,
				bidsectionguid);
		// 提示词增加招标要求内容
		StringBuilder sb_require=new StringBuilder();
		if (requirement != null && !requirement.isEmpty()) {
			sb_require.append("已知招标文件中本项目的");
			for (BidAIRequirementDTO r : requirement) {
				sb_require.append("“" + r.getRequireName() + "”是" + r.getRequireValue() + "，");
			}
			sb_require.append("\n");
		}
		return sb_require;
	}
}
