package com.epoint.ebpu.AIClientSDK.client.vo.deepseek;

import com.epoint.ebpu.AIClientSDK.client.vo.ChatMessage;

import java.util.List;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-02
 * @Version
 * @Description
 */
public class ResponseDataVO {
    //该对话的唯一标识符
    private String id;
    //模型生成的 completion 的选择列表
    private List<Choice> choices;
    //创建聊天完成时的 Unix 时间戳（以秒为单位）
    private long created;
    //生成该 completion 的模型名
    private String model;
    //对象的类型, 如聊天型chat.completion
    private String object;
    //该对话补全请求的用量信息
    private Usage usage;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<Choice> getChoices() {
        return choices;
    }

    public void setChoices(List<Choice> choices) {
        this.choices = choices;
    }

    public long getCreated() {
        return created;
    }

    public void setCreated(long created) {
        this.created = created;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public Usage getUsage() {
        return usage;
    }

    public void setUsage(Usage usage) {
        this.usage = usage;
    }

    public static class  Choice{
        /*
        模型停止生成 token 的原因。
        stop：模型自然停止生成，或遇到 stop 序列中列出的字符串。
        length ：输出长度达到了模型上下文长度限制，或达到了 max_tokens 的限制。
        content_filter：输出内容因触发过滤策略而被过滤。
        insufficient_system_resource：系统推理资源不足，生成被打断。*/
        private String finish_reason;
        //该 completion 在模型生成的 completion 的选择列表中的索引
        private int index;
        //模型生成的 completion 消息。
        private ChatMessage message;

        public String getFinish_reason() {
            return finish_reason;
        }

        public void setFinish_reason(String finish_reason) {
            this.finish_reason = finish_reason;
        }

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        public ChatMessage getMessage() {
            return message;
        }

        public void setMessage(ChatMessage message) {
            this.message = message;
        }
    }

    public static class Usage{
        //模型 completion 产生的 token 数。就是输出token数
        private int completion_tokens;
        //用户 prompt 所包含的 token 数。
        private int prompt_tokens;
        //该请求中，所有 token 的数量（prompt + completion）。
        private int total_tokens;

        public int getCompletion_tokens() {
            return completion_tokens;
        }

        public void setCompletion_tokens(int completion_tokens) {
            this.completion_tokens = completion_tokens;
        }

        public int getPrompt_tokens() {
            return prompt_tokens;
        }

        public void setPrompt_tokens(int prompt_tokens) {
            this.prompt_tokens = prompt_tokens;
        }

        public int getTotal_tokens() {
            return total_tokens;
        }

        public void setTotal_tokens(int total_tokens) {
            this.total_tokens = total_tokens;
        }
    }
}
