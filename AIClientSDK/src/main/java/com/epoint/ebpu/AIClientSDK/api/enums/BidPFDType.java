package com.epoint.ebpu.AIClientSDK.api.enums;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-25
 * @Version
 * @Description
 */
public enum BidPFDType {
    All("","所有"),
    FHX("fhx","符合性评审"),
    MARK("mark","打分评审")
    ;
    private String pfdType;
    private String pfdTypeDescription;

    BidPFDType(String pfdType, String pfdTypeDescription) {
        this.pfdType = pfdType;
        this.pfdTypeDescription = pfdTypeDescription;
    }

    public static BidPFDType getByPFDType(String pfdtype){
        for (BidPFDType value : values()) {
            if(value.getPfdType().equals(pfdtype)){
                return value;
            }
        }
        return All;
    }

    public String getPfdType() {
        return pfdType;
    }

    public String getPfdTypeDescription() {
        return pfdTypeDescription;
    }
}
