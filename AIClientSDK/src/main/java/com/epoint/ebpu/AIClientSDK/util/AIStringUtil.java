package com.epoint.ebpu.AIClientSDK.util;

import org.apache.commons.lang.StringUtils;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-25
 * @Version
 * @Description
 */
public class AIStringUtil {
	/**
	 * 处理大模型返回的表格字段
	 * @param str
	 * @return
	 */
    public static String dealEmptyStr(String str){
        if(StringUtils.isBlank(str)||"无".equals(str)||"null".equals(str)||"''".equals(str)){
            return "";
        }
        return str;
    }
    
    /**
	 * 处理输入大模型的表格字段
	 * @param str
	 * @return
	 */
    public static String dealEmptyStr2Model(String str){
        if(StringUtils.isBlank(str)||"无".equals(str)||"null".equals(str)||"''".equals(str)){
            return "/";
        }
        return str;
    }

    public static String trimEnd(String originalStr,char trimStr){
        if (StringUtils.isBlank(originalStr)) {
            return originalStr;
        }

        int end = originalStr.length();
        // 从末尾开始查找字符 ch
        while (end > 0 && originalStr.charAt(end - 1) == trimStr) {
            end--;
        }

        // 返回去除指定字符后的子字符串
        return originalStr.substring(0, end);
    }
}
