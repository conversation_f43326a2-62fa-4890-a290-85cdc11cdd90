package com.epoint.ebpu.AIClientSDK.controller.dto;

import java.util.List;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-25
 * @Version
 * @Description
 */
public class ReqGetassesstableDTO {
    private String accesstoken;
    private String platformcode;
    private String bidsectionguid;
    private String tenderguid;
    private List<String> bidassessitemguids;
    private String usetype;

    public String getAccesstoken() {
        return accesstoken;
    }

    public void setAccesstoken(String accesstoken) {
        this.accesstoken = accesstoken;
    }

    public String getPlatformcode() {
        return platformcode;
    }

    public void setPlatformcode(String platformcode) {
        this.platformcode = platformcode;
    }

    public String getBidsectionguid() {
        return bidsectionguid;
    }

    public void setBidsectionguid(String bidsectionguid) {
        this.bidsectionguid = bidsectionguid;
    }

    public String getTenderguid() {
        return tenderguid;
    }

    public void setTenderguid(String tenderguid) {
        this.tenderguid = tenderguid;
    }

    public List<String> getBidassessitemguids() {
        return bidassessitemguids;
    }

    public void setBidassessitemguids(List<String> bidassessitemguids) {
        this.bidassessitemguids = bidassessitemguids;
    }

    public String getUsetype() {
        return usetype;
    }

    public void setUsetype(String usetype) {
        this.usetype = usetype;
    }
}
