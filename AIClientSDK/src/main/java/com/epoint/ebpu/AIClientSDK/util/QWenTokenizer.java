package com.epoint.ebpu.AIClientSDK.util;

import org.springframework.core.io.DefaultResourceLoader;

import java.io.BufferedReader;
import java.io.InputStreamReader;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-25
 * @Version
 * @Description
 */
public class QWenTokenizer {

    /**
     * 调用Python脚本并计算输入文本的token长度
     *
     * @param inputText 输入文本
     * @return token长度
     * @throws Exception 如果执行失败
     */
    public static int getTokenLength(String inputText) {
        try {
            String pythonScriptPath=new DefaultResourceLoader().getResource("classpath:py\\tokenization_qwen" +
                    ".py").getFile().getAbsolutePath();
            // 构建命令
            String command = "python " + pythonScriptPath + " \"" + inputText + "\"";

            // 执行命令
            Process process = Runtime.getRuntime().exec(command);

            // 读取Python脚本的输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String output = reader.readLine();

            // 检查是否有错误输出
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String errorOutput = errorReader.readLine();
            if (errorOutput != null) {
                throw new RuntimeException("Python script error: " + errorOutput);
            }

            // 返回token长度
            return Integer.parseInt(output.trim());
        }
        catch (Exception ex){
            throw new RuntimeException(ex);
        }

    }

    public static void main(String[] args) {
        try {
            // 测试输入文本
            String inputText = "Hello, how are you doing today?";
            int tokenLength = getTokenLength(inputText);

            // 输出结果
            System.out.println("Token length: " + tokenLength);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
