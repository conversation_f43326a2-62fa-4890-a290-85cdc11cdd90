package com.epoint.ebpu.AIClientSDK.dto;

import java.util.List;

public class BidAIEvaluationResultPack {
	private long inputTokens;
	private long outputTokens;
	private List<BidAIEvaluationResultDTO> data;

	public BidAIEvaluationResultPack(long in, long out, List<BidAIEvaluationResultDTO> data) {
		this.inputTokens = in;
		this.outputTokens = out;
		this.data = data;
	}

	public long getInputTokens() {
		return inputTokens;
	}

	public void setInputTokens(long inputTokens) {
		this.inputTokens = inputTokens;
	}

	public long getOutputTokens() {
		return outputTokens;
	}

	public void setOutputTokens(long outputTokens) {
		this.outputTokens = outputTokens;
	}

	public List<BidAIEvaluationResultDTO> getData() {
		return data;
	}

	public void setData(List<BidAIEvaluationResultDTO> data) {
		this.data = data;
	}

}
