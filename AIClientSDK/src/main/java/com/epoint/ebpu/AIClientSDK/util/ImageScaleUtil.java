package com.epoint.ebpu.AIClientSDK.util;

import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

import javax.imageio.ImageIO;

/**
 * 图片分辨率处理工具
 * <AUTHOR>
 *
 */
public class ImageScaleUtil {
	// 大模型图片限制分辨率
	public static int LIMITPIXEL = 36000000;

	/**
	 * 判断图片分辨率是否超出大模型要求 
	 * 自动关掉输入流
	 * 
	 * @param input
	 * @return
	 */
	public static boolean isExceedLimitPixels(InputStream input) {
		try {
			BufferedImage origin = ImageIO.read(input);
			int width = origin.getWidth();
			int height = origin.getHeight();
			if (width * height > LIMITPIXEL) {
				return true;
			}
			return false;
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		} finally {
			try {
				if (input != null) {
					input.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 处理图片缩小分辨率到限制大小内 使用ImageIO.write写入新图片
	 * 自动关掉输入流
	 * 
	 * @param input
	 * @return
	 */
	public static BufferedImage scalePixels(InputStream input) {
		try {
			BufferedImage origin = ImageIO.read(input);
			int width = origin.getWidth();
			int height = origin.getHeight();
			if (width * height <= LIMITPIXEL) {
				return origin;
			}
			// 循环缩小1%直至满足分辨率限制
			while (width * height > LIMITPIXEL) {
				width = (int) (0.99 * width);
				height = (int) (0.99 * height);
			}
			// 创建缩放后的图片
			BufferedImage resizedImage = new BufferedImage(width, height, origin.getType());
			resizedImage.createGraphics();

			// 使用AffineTransformOp进行缩放
			AffineTransform at = AffineTransform.getScaleInstance((double) width / origin.getWidth(),
					(double) height / origin.getHeight());
			AffineTransformOp scaleOp = new AffineTransformOp(at, AffineTransformOp.TYPE_BILINEAR);
			scaleOp.filter(origin, resizedImage);
			return resizedImage;
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		} finally {
			try {
				if (input != null) {
					input.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	public static void main(String[] args) throws IOException {
		InputStream fin = new FileInputStream(new File("C:\\Users\\<USER>\\Downloads\\yyzz.jpg"));
		if (ImageScaleUtil.isExceedLimitPixels(fin)) {
			// 已关闭流重新初始化
			fin = new FileInputStream(new File("C:\\Users\\<USER>\\Downloads\\yyzz.jpg"));
			System.out.println("checkin");
			BufferedImage newimage = ImageScaleUtil.scalePixels(fin);
			ImageIO.write(newimage, "jpg", new File("C:\\Users\\<USER>\\Downloads\\yyzz2.jpg"));
		}
	}
}
