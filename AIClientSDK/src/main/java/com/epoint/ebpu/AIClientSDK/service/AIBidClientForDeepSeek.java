package com.epoint.ebpu.AIClientSDK.service;

import com.epoint.ebpu.AIClientSDK.client.DeepSeekAiUtils;
import com.epoint.ebpu.AIClientSDK.client.QwenAiUtils;
import com.epoint.ebpu.AIClientSDK.client.enums.DomainType;
import com.epoint.ebpu.AIClientSDK.client.enums.MessageType;
import com.epoint.ebpu.AIClientSDK.client.prompts.DSPrompts;
import com.epoint.ebpu.AIClientSDK.client.vo.ChatMessage;
import com.epoint.ebpu.AIClientSDK.client.vo.ResultVO;
import com.epoint.ebpu.AIClientSDK.dto.*;
import com.epoint.ebpu.AIClientSDK.util.AIStringUtil;
import com.epoint.ebpu.AIClientSDK.util.MarkdownUtil;
import com.epoint.ebpu.AIClientSDK.util.PDFConvertStringUtil;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Title
 * <AUTHOR>
 * @Date 2025-04-02
 * @Version
 * @Description
 */
public class AIBidClientForDeepSeek extends AIBidClient{
    protected static final org.slf4j.Logger logger = LoggerFactory.getLogger(AIBidClientForDeepSeek.class);

    /**
     * 招标输入长度，64上下文，8输出长度，2为提示词长度
     *  注：deepseek的思维链内容不计入上下文
     */
    protected static final int MAXLENGTH_INPUT_ZB=(int)((64-8-2)*1000/0.75);

    @Override
    public BidAIAssessMethodPack pbbfStripper(InputStream zbpdf) {
        logger.info("DeepSeek大模型sdk评标办法提取--开始");
        // pdf转文本
        String zbContent = truncateAiInputStr(PDFConvertStringUtil.parseByWordCom(zbpdf));
        // 大模型提取评标办法
        String systemPrompt= DSPrompts.getPBBFStripPrompt();
        ResultVO resultVO=QwenAiUtils.callAndLogtime(logger,"deepseek大模型-招标文件中提取评标办法",
                r->DeepSeekAiUtils.chatCompletions(ConstantsConfig.config.overallModel,new ChatMessage(MessageType.system, systemPrompt),
                        new ChatMessage(MessageType.user, r)),zbContent);
        String pbbfmd = resultVO.getData();
        // md转对象，评标办法条款“评审因素”“评审标准”“评审项”“评审方式”“分值”
        List<Map<String, String>> pbbf = MarkdownUtil.md2List(pbbfmd);
        // 解析后按标准格式返回
        List<BidAIAssessMethodDTO> lst = new ArrayList<>();
        for (Map<String, String> r : pbbf) {
            BidAIAssessMethodDTO p = new BidAIAssessMethodDTO();
            p.setAssessItemName(r.get("评审因素"));
            p.setItemCriteria(r.get("评审标准"));
            p.setBlockTag(r.get("评审项"));
            p.setAssessType(r.get("评审方式"));
            p.setMark(r.get("分值"));
            lst.add(p);
        }
        logger.info("DeepSeek大模型sdk评标办法提取--结束，" + lst.size() + "条");
        return new BidAIAssessMethodPack(resultVO.getInputTokens(),resultVO.getOutputTokens(),lst);
    }

    @Override
    public BidAIAssessRuleReasonPack assessRuleReasoning(String assessName, String itemCriteria) {
        logger.info("DeepSeek大模型sdk评审规则推理--开始");
        String systemPrompt= DSPrompts.getRuleReasoningPrompt();
        String question = "评审点：" + assessName + "\n评审标准：" + itemCriteria;
        ResultVO resultVO=QwenAiUtils.callAndLogtime(logger,"deepseek大模型-评审规则推理",
                r->DeepSeekAiUtils.chatCompletions(ConstantsConfig.config.overallModel,
                        new ChatMessage(MessageType.system, systemPrompt),
                        new ChatMessage(MessageType.user, r)),question);
        logger.info("DeepSeek大模型sdk评审规则推理--结束");
        return new BidAIAssessRuleReasonPack(resultVO.getInputTokens(),resultVO.getOutputTokens(),resultVO.getData());
    }

    @Override
    public BidAIRequirementPack bidFileDataStripper(InputStream zbpdf, List<BidAIRequirementDTO> requirement) {
        logger.info("deepseek大模型sdk招标项目信息提取--开始");
        StringBuilder zbExtract = new StringBuilder();
        zbExtract.append("请提取出招标文件中");
        // 拼接输入的字段名
        for (BidAIRequirementDTO r : requirement) {
            zbExtract.append("“" + r.getRequireName() + "”");
        }
        zbExtract.append("的信息，");
        // 招标文件内容拼接
        zbExtract.append("招标文件内容如下：");
        String zbContent = truncateAiInputStr(PDFConvertStringUtil.parseByWordCom(zbpdf));
        zbExtract.append(zbContent);
        // 调用大模型提取
        String systemPrompt= DSPrompts.getBidFileDataStripperPrompt();
        ResultVO resultVO=QwenAiUtils.callAndLogtime(logger,"deepseek大模型-招标项目信息提取",
                r->DeepSeekAiUtils.chatCompletions(ConstantsConfig.config.overallModel,
                        new ChatMessage(MessageType.system, systemPrompt),
                        new ChatMessage(MessageType.user, r)),zbExtract.toString());

        // md转对象，“字段”“内容”
        List<Map<String, String>> requiremd = MarkdownUtil.md2List(resultVO.getData());
        // 解析后按标准格式返回
        List<BidAIRequirementDTO> lst = new ArrayList<>();
        for (Map<String, String> r : requiremd) {
            BidAIRequirementDTO p = new BidAIRequirementDTO();
            p.setRequireName(r.get("字段"));
            p.setRequireValue(r.get("内容"));
            lst.add(p);
        }
        logger.info("deepseek大模型sdk招标项目信息提取--结束，" + lst.size() + "条");
        return new BidAIRequirementPack(resultVO.getInputTokens(),resultVO.getOutputTokens(),lst);
    }

    @Override
    public BidAIAssessItemFactorPack requirementContentStripper(InputStream zbpdf, List<BidAIAssessItemFactorDTO> assessItemFactor) {
        if (assessItemFactor == null || assessItemFactor.isEmpty()) {
            return null;
        }
        logger.info("deepseek大模型sdk招标要求内容提取--开始");
        // 招标文件内容
        String zbContent = truncateAiInputStr(PDFConvertStringUtil.parseByWordCom(zbpdf));
        // 评审规则表（招标部分）
        StringBuilder rules = new StringBuilder();
        rules.append("请解析下招标要求信息，招标文件内容如下：").append("\n");
        rules.append(zbContent).append("\n");
        rules.append("评审规则表如下:").append("\n");
        rules.append("|唯一标识|序号|审查点|评审要求|招标文件规定|").append("\n");
        rules.append("| ---- | ---- | ---- | ---- | ---- |").append("\n");
        for (BidAIAssessItemFactorDTO pfd : assessItemFactor) {
            rules.append("|" + pfd.getItemGuid()).append("|" + pfd.getItemOrder()).append("|" + pfd.getAssessItemName())
                    .append("|" + AIStringUtil.dealEmptyStr(pfd.getItemCriteria())).append("|" + AIStringUtil.dealEmptyStr(pfd.getBidRequirements()) )
                    .append("|").append("\n");
        }
        String systemPrompt= DSPrompts.getRequirementContentStripperPrompt();
        ResultVO resultVO=QwenAiUtils.callAndLogtime(logger,"deepseek大模型-招标要求内容提取",
                r->DeepSeekAiUtils.chatCompletions(ConstantsConfig.config.overallModel,
                        new ChatMessage(MessageType.system, systemPrompt),
                        new ChatMessage(MessageType.user, r)),rules.toString());
        String zbresult = resultVO.getData();
        // md转对象
        List<Map<String, String>> requiremd = MarkdownUtil.md2List(zbresult);
        // 解析后按标准格式返回
        List<BidAIAssessItemFactorDTO> lst = new ArrayList<>();
        for (Map<String, String> r : requiremd) {
            BidAIAssessItemFactorDTO p = new BidAIAssessItemFactorDTO();
            p.setItemGuid(r.get("唯一标识"));
            p.setItemOrder(r.get("序号"));
            p.setAssessItemName(r.get("审查点"));
            p.setItemCriteria(AIStringUtil.dealEmptyStr(r.get("评审要求")));
            p.setBidRequirements(AIStringUtil.dealEmptyStr(r.get("招标文件规定")));
            lst.add(p);
        }
        logger.info("deepseek大模型sdk招标要求内容提取--结束，" + lst.size() + "条");
        return new BidAIAssessItemFactorPack(resultVO.getInputTokens(),resultVO.getOutputTokens(),lst);
    }

    @Override
    @Deprecated
    public BidAIAssessItemFactorPack responseContentStripper(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAITenderFileDTO> tenderFiles) {
        throw new RuntimeException("未支持的模型调用");
    }

    @Override
    @Deprecated
    public BidAIEvaluationResultPack evaluation(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAIRequirementDTO> requirement, DomainType type) {
        throw new RuntimeException("未支持的模型调用");
    }

    @Override
    public BidAIEvaluationResultPack evaluation(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAIRequirementDTO> requirement, List<BidAITenderFileDTO> tenderFiles, DomainType type) {
        throw new RuntimeException("未支持的模型调用");
    }

    @Override
    public BidAIEvaluationResultPack evaluationformark(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAIRequirementDTO> requirement, List<BidAITenderFileDTO> tenderFiles, DomainType type) {
        throw new RuntimeException("未支持的模型调用");
    }

    protected String truncateAiInputStr(String inputStr){
        if(inputStr.length()>MAXLENGTH_INPUT_ZB){
            //超过长度进行截断
            logger.info("文件超长被截断，展示文件前文件前200字用于查找："+inputStr.substring(0,200));
            return inputStr.substring(0,MAXLENGTH_INPUT_ZB);
        }
        return inputStr;
    }

    @Override
    public BidAIHorizontalEvalPack tenderHorizontalEval(BidAIAssessItemFactorDTO assessItemFactor, List<BidAIRequirementDTO> requirement, DomainType type, List<BidAIHorizontalEvalDTO> preAIEvaluations) {
        return null;
    }

    public static class ConstantsConfig{
        public static ConstantsConfig config;

        static {
            config =new ConstantsConfig();
            config.overallModel=DeepSeekAiUtils.CHAT_MODEL;
        }

        private String overallModel;

        public String getOverallModel() {
            return overallModel;
        }

        public void setOverallModel(String overallModel) {
            this.overallModel = overallModel;
        }
    }
}
