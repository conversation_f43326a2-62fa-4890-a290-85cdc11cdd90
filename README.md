# AIClientSDK

商用大模型智能体平台的客户端调用封装，选定两个大模型开放平台，闭源的doubao和开源的Qwen系列

### 模型调用

##### doubao系列

长文本：doubao1.5-pro-256k  支持256K tokens上下文

多模态：doubao1.5-version-pro  支持128K tokens上下文

限制问题：多模态一次输入20张图片，pdf每一页预处理成1K tokens的图片

##### qwen系列

长文本：qwen2.5-32B  支持128K tokens上下文

多模态：qwen2.5-VL-32B  支持128K tokens上下文

限制问题：多模态一次输入20张图片（虽然能输入更多，但回复准确性会下降），pdf每一页预处理成1K tokens的图片

### 客户端初始化方式

AIBidClient client = AIBidClientFactory.getClient(AIModelType.DOUBAO);

AIModelType枚举类可选择是走doubao还是qwen的接口

### 使用场景

场景见api接口，分为1、评标办法提取 2、招标要求提取 3、项目信息提取 4、评审规则推理 5、投标响应提取 6、符合性智能评审  7、客观分打分智能评审

智能评审要注意领域类型走不同的智能体，DomainType枚举类中选中是工程类、采购类等

##### 场景一 评标办法提取

输入招标文件pdf，使用wordcom提取出正文文本Markdown，调用提取评标办法智能体，将前附表或分散的多个附表提取出评标办法的“评审项、评审因素、评审标准、打分方式、分值”数据。

注意：这里提取的是正文原文，评审标准依旧是“符合投标人须知XXX项规定”，具体的规定是什么需要用“招标要求内容提取”接口。

调用方法：

```
BidAIAssessMethodPack pbbfStripper(InputStream zbpdf)
```

##### 场景二 招标要求提取

由于评标办法提取后是正文原文，有些评分点的评审标准是没有写清具体的招标要求，需要使用该接口进一步提取出详细的招标要求内容，输入招标文件pdf和评标办法转换成的评审规则表，使用wordcom提取出正文文本Markdown，调用提取招标要求智能体，将评审标准是“符合投标人须知XXX项规定”“见招标公告的质量要求”等描述的从正文中提取出具体化的要求内容补充，并且将招标文件规定的关联信息，如评审需要用到的“本项目的开标时间、项目专业”等一并提取出来。

调用方法：评审规则BidAIAssessItemFactorDTO中用到 唯一标识、序号、审查点、评审要求、招标文件规定 字段

```
BidAIAssessItemFactorPack requirementContentStripper(InputStream zbpdf, List<BidAIAssessItemFactorDTO> assessItemFactor)
```

##### 场景三 项目信息提取

输入招标文件pdf，使用wordcom提取出正文文本Markdown，调用提取项目信息智能体，将按要求字段动态提取招标正文中的项目信息，可以作为评审时项目基本信息输入。

调用方法：

```
BidAIRequirementPack bidFileDataStripper(InputStream zbpdf, List<BidAIRequirementDTO> requirement)
```

##### 场景四 评审规则推理

将评审因素的名称、评审标准输入，调用推理评审规则智能体，按参考的示例输出评审规则判定逻辑，判定的对象和逻辑取用输入的评审标准，可以修改后多次尝试完善。

调用方法：

```
BidAIAssessRuleReasonPack assessRuleReasoning(String assessName, String itemCriteria)
```

##### 场景五 投标响应提取

输入预处理好的投标材料图片和评审规则表，调用提取投标响应情况智能体，将按评审规则表的评审因素、评审标准、投标响应情况的关联信息从投标文件材料中提取出每个评审对应的响应内容

调用方法：评审规则BidAIAssessItemFactorDTO中用到 唯一标识、序号、审查点、评审指标、投标响应情况 字段

```
 BidAIAssessItemFactorPack responseContentStripper(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAITenderFileDTO> tenderFiles)
```



#### 场景六 智能评审

智能评审分为符合性评审、客观性打分、主观性打分

- 符合性评审：一般是初步评审的资格审查、形式响应性等评审，依照评审规则表判定投标材料中响应内容有没有符合招标要求，输出的是“合格/不合格/提请人工判断”的评审结论和理由。

调用方法：requirement的项目基本信息可传null，DomainType枚举选择领域

```
BidAIEvaluationResultPack evaluation(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAIRequirementDTO> requirement, List<BidAITenderFileDTO> tenderFiles, DomainType type)
```

- 客观性打分：一般是业绩、信誉、资产评估等评审，依照评审规则表判定投标材料中有多少响应内容是达标的得分点，输出的是“分数”和理由。

调用方法：requirement的项目基本信息可传null，DomainType枚举选择领域

```
BidAIEvaluationResultPack evaluationformark(List<BidAIAssessItemFactorDTO> assessItemFactor, List<BidAIRequirementDTO> requirement, List<BidAITenderFileDTO> tenderFiles, DomainType type)
```

- 主观性打分：一般是技术标评审，评审的是技术方案的全面性、针对性、合理性、创新技术加分等，是项目建设过程各方面的综合考量评价，输出的是“分数”和理由。

